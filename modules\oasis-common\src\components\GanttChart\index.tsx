import { type PropType, computed, defineComponent, onBeforeUnmount, ref, shallowRef } from 'vue';
import type { JSX } from 'vue/jsx-runtime';
import { ResizeItem } from '../ResizeItem';

interface GanttItem {
  id: string | number;
  label?: string;
  start: number;
  end: number;
  meta?: any;
}

const GanttChart = defineComponent({
  props: {
    items: {
      type: Array as PropType<GanttItem[]>,
      required: true,
    },
    min: {
      type: Number as PropType<number>,
      required: true,
    },
    max: {
      type: Number as PropType<number>,
      required: true,
    },
    height: {
      type: Number as PropType<number>,
      default: 400,
    },
    /**
     * 甘特图条目的行高
     */
    rowHeight: {
      type: Number as PropType<number>,
      default: 40,
    },
    /**
     * 甘特图条目之间的行间距
     */
    rowGap: {
      type: Number as PropType<number>,
      default: 20,
    },
    /**
     * 坐标轴刻度精度，单位为数值（同时也是最小移动单位）
     */
    precision: {
      type: Number as PropType<number>,
      default: 1,
    },
    /**
     * 是否显示坐标轴分割线
     * 默认为 true
     */
    showAxisDivider: {
      type: Boolean as PropType<boolean>,
      default: true,
    },
    /**
     * 坐标轴刻度间隔
     */
    tickInterval: {
      type: Number as PropType<number>,
      default: 10,
    },
    /**
     * 是否允许条目调整大小或者拖拽（仅调整当前条目位置，无法上下移动）
     */
    resizeable: {
      type: Boolean as PropType<boolean>,
      default: false,
    },
    /**
     * 坐标轴标签格式化函数
     * @param tick 坐标轴刻度值
     * @returns 格式化后的字符串
     */
    axisLabelFormat: {
      type: Function as PropType<(tick: number) => string>,
      default: (tick: number) => tick.toString(),
    },
    /**
     * 渲染每个甘特条目的函数
     * @param item 甘特条目数据
     * @param rect 条目在图表中的位置和大小
     * @returns 返回一个 JSX 元素
     */
    renderItem: {
      type: Function as PropType<(item: GanttItem, rect: { x: number; width: number; y: number; height: number }) => JSX.Element>,
      default: undefined,
    },
    /**
     * 渲染坐标轴的函数
     * @param tick 坐标轴刻度值
     * @param index 坐标轴刻度索引
     * @returns 返回一个 JSX 元素
     */
    renderAxis: {
      type: Function as PropType<(tick: number, index: number) => JSX.Element>,
    },
    /**
     * 更新条目的回调函数, 仅当 `resizeable` 为 true 时有效
     * @param item 被更新的甘特条目
     * @param newStart 新的开始位置
     * @param newEnd 新的结束位置
     */
    onUpdate: {
      type: Function as PropType<(item: GanttItem, newStart: number, newEnd: number) => void>,
    },
  },
  setup(props) {
    const range = computed(() => props.max - props.min);
    const scaleX = (val: number) => ((val - props.min) / range.value) * 100;
    const ticks = computed(() => {
      const arr: number[] = [];
      for (let t = props.min; t <= props.max; t += props.tickInterval) {
        arr.push(t);
      }
      return arr;
    });
    const containerRef = ref<HTMLElement | null>(null);
    // 拖拽状态管理
    const dragTempState = ref<Record<string | number, { start: number; end: number }>>({});
    // 当前拖拽的项
    const dragging = shallowRef<{
      item: GanttItem;
      startX: number;
      origStart: number;
      origEnd: number;
    } | null>(null);
    // 当前调整大小的项
    const resizing = shallowRef<{
      item: GanttItem;
      edge: 'start' | 'end';
      startX: number;
      origStart: number;
      origEnd: number;
    } | null>(null);
    // 计算内部内容撑起的高度
    const contentHeight = computed(() => {
      return props.items.length * props.rowHeight + (props.items.length - 1) * props.rowGap + 20; // 20为顶部轴高度
    });

    const onMouseMove = (e: MouseEvent) => {
      if (!containerRef.value) {
        return;
      }
      const rect = containerRef.value.getBoundingClientRect();
      const deltaPx = e.clientX - (dragging.value?.startX ?? resizing.value?.startX ?? 0);
      const deltaPercent = (deltaPx / rect.width) * 100;
      const deltaCoord = (deltaPercent / 100) * range.value;

      if (dragging.value) {
        const width = dragging.value.origEnd - dragging.value.origStart;
        const newStart = dragging.value.origStart + deltaCoord;
        const newEnd = newStart + width;
        dragTempState.value[dragging.value.item.id] = { start: newStart, end: newEnd };
      }

      if (resizing.value) {
        const { item, edge, origStart, origEnd } = resizing.value;
        let newStart = origStart;
        let newEnd = origEnd;

        if (edge === 'start') {
          newStart = Math.min(origEnd, Math.max(props.min, origStart + deltaCoord));
        } else {
          newEnd = Math.max(origStart, Math.min(props.max, origEnd + deltaCoord));
        }

        dragTempState.value[item.id] = { start: newStart, end: newEnd };
      }
    };

    const onMouseUp = () => {
      const state = dragging.value || resizing.value;
      if (state && props.onUpdate) {
        const { item, origStart, origEnd } = state;
        const temp = dragTempState.value[item.id];
        if (temp && (temp.start !== origStart || temp.end !== origEnd)) {
          const precision = props.precision;
          const adjustedStart = Math.round(temp.start / precision) * precision;
          const adjustedEnd = Math.round(temp.end / precision) * precision;
          props.onUpdate(item, adjustedStart, adjustedEnd);
        }

        if (dragging.value?.item) {
          delete dragTempState.value[dragging.value.item.id];
        }
        if (resizing.value?.item) {
          delete dragTempState.value[resizing.value.item.id];
        }
      }

      dragging.value = null;
      resizing.value = null;

      window.removeEventListener('mousemove', onMouseMove);
      window.removeEventListener('mouseup', onMouseUp);
    };

    onBeforeUnmount(() => {
      window.removeEventListener('mousemove', onMouseMove);
      window.removeEventListener('mouseup', onMouseUp);
    });

    const renderAxis = () => {
      return (
        <div class="pos-absolute left-0 top-0 w-full flex items-center justify-between" style={{ height: `${contentHeight.value}px` }}>
          {ticks.value.map((tick, index) => {
            const left = scaleX(tick);
            return props.renderAxis
              ? (
                props.renderAxis(tick, index)
              )
              : (
                <>
                  <div
                    class="pos-absolute top-0 h-full flex flex-col"
                    key={tick}
                    style={{
                      left: `${left}%`,
                    }}
                  >
                    {props.axisLabelFormat(tick)}
                    {props.showAxisDivider && (
                      <div class="w-1px flex-1 bg-FO-Brand-Tertiary-Active" />
                    )}
                  </div>
                </>
              );
          })}
        </div>
      );
    };

    const renderItem = (item: GanttItem, index: number) => {
      const temp = dragTempState.value[item.id];
      const start = temp?.start ?? item.start;
      const end = temp?.end ?? item.end;
      const x = scaleX(start);
      const width = scaleX(end) - x;
      const y = index * props.rowHeight;
      const barHeight = props.rowHeight - 4;

      const itemStyle = {
        position: 'absolute',
        left: `${x}%`,
        height: `${barHeight}px`,
        width: `${width}%`,
        cursor: props.resizeable ? 'grab' : 'default',
        transition: dragging.value ? 'none' : 'transform 0.1s ease',
      };

      const onMouseDown = (e: MouseEvent) => {
        dragging.value = {
          item,
          startX: e.clientX,
          origStart: item.start,
          origEnd: item.end,
        };
        window.addEventListener('mousemove', onMouseMove);
        window.addEventListener('mouseup', onMouseUp);
      };

      const onResizeStart = (e: MouseEvent, edge: 'start' | 'end') => {
        e.stopPropagation();
        resizing.value = {
          item,
          edge,
          startX: e.clientX,
          origStart: item.start,
          origEnd: item.end,
        };
        window.addEventListener('mousemove', onMouseMove);
        window.addEventListener('mouseup', onMouseUp);
      };

      return (
        <div class="pos-relative flex" style={{ minHeight: `${props.rowHeight}px`, height: `${props.rowHeight}px` }}>
          <ResizeItem
            class="flex-1"
            containerStyle={itemStyle}
            onMouseDown={onMouseDown}
            onResizeStart={onResizeStart}
            resizeable={props.resizeable}
          >
            {
              props.renderItem
                ? props.renderItem(item, { x, width, y, height: props.rowHeight })
                : (
                  <div>{item.label || item.id}</div>
                )
            }
          </ResizeItem>
        </div>
      );
    };

    return () => (
      <div
        class="gantt-chart overflow-x-hidden overflow-y-scroll"
        ref={containerRef}
        style={{ height: `${props.height}px` }}
      >
        <div class="pos-relative h-full flex flex-col pt-20px" style={{ gap: `${props.rowGap}px` }}>
          {renderAxis()}
          {props.items.map(renderItem)}
        </div>
      </div>
    );
  },
});

export {
  GanttChart,
};
export type { GanttItem };
