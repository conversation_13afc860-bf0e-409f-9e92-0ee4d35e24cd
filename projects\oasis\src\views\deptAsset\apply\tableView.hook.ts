import type { BasicColumn } from '/@/components/Table';
import { formatNickName } from '/@/hooks/system/useUserList';

export function usetableView() {
  let columns: BasicColumn[] = [];
  const getColumns = (tab: string): BasicColumn[] => {
    columns = [{
      title: '设备名称',
      dataIndex: 'deviceName',
      width: 300,
      fixed: 'left',
      ifShow: true,
    }, {
      title: '资产编号',
      dataIndex: 'assetNo',
      width: 150,
      ifShow: true,
    }, {
      title: 'UDID/序列号',
      dataIndex: 'deviceUDID',
      width: 350,
      ifShow: false,
    }, {
      title: '唯一标识',
      dataIndex: 'deviceUID',
      width: 150,
      ifShow: false,
    }, {
      title: '设备型号',
      dataIndex: 'deviceType',
      width: 150,
      ifShow: false,
    }, {
      title: '设备品牌',
      dataIndex: 'brandID',
      width: 150,
      ifShow: false,
    }, {
      title: '跑分',
      dataIndex: 'chipset',
      width: 80,
      ifShow: ['allDevice'].includes(tab),
    }, {
      title: '系统',
      dataIndex: 'osVersion',
      width: 150,
      ifShow: ['allDevice'].includes(tab),
    }, {
      title: 'CPU',
      dataIndex: 'socName',
      width: 200,

      ifShow: ['allDevice'].includes(tab),
    }, {
      title: 'GPU',
      dataIndex: 'gpuType',
      width: 200,
      ifShow: ['allDevice'].includes(tab),
    }, {
      title: '运行内存',
      dataIndex: 'ram',
      width: 80,
      format: (_, record) => `${record.ram}G`,
      ifShow: ['allDevice'].includes(tab),
    }, {
      title: '机身内存',
      dataIndex: 'memory',
      width: 80,
      format: (_, record) => `${record.memory}G`,
      ifShow: ['allDevice'].includes(tab),
    }, {
      title: '分辨率',
      dataIndex: 'resolution',
      width: 120,
      ifShow: ['allDevice'].includes(tab),
    }, {
      title: '所属人',
      dataIndex: 'owner',
      width: 150,
      format: (_, record) => formatNickName(record.owner),
      ifShow: ['allDevice', 'deviceManage', 'myOccupy'].includes(tab),
    }, {
      title: '所属部门',
      dataIndex: 'dept',
      width: 200,
      format: (_, record) => record.dept?.orgPath || '',
      ifShow: ['deviceManage'].includes(tab),
    }, {
      title: '资产用途',
      dataIndex: 'usage',
      width: 150,
      ifShow: false,
    }, {
      title: '设备类型',
      dataIndex: 'mobileType',
      width: 80,
      ifShow: false,
    }, {
      title: '资产分类',
      dataIndex: 'assetType',
      width: 80,
      ifShow: false,
    }, {
      title: '流通级别',
      dataIndex: 'accessLevel',
      width: 200,
      ifShow: ['deviceManage', 'myDevice'].includes(tab),
    }, {
      title: '备注',
      dataIndex: 'remark',
      width: 200,
      ifShow: ['myDevice'].includes(tab),
    }, {
      title: '使用人',
      dataIndex: 'currentUserID',
      width: 150,
      ifShow: ['allDevice', 'myDevice'].includes(tab),
    }, {
      title: '已使用天数',
      dataIndex: 'useDays',
      width: 150,
      ifShow: ['myDevice', 'myOccupy'].includes(tab),
    }, {
      title: '预计归还时间',
      dataIndex: 'returnTime',
      width: 250,
      ifShow: ['allDevice', 'myDevice', 'myOccupy'].includes(tab),
    }, {
      title: '状态',
      dataIndex: 'fsmState',
      width: 100,
      fixed: 'right',
      ifShow: true,
    }, {
      title: '操作',
      dataIndex: 'action',
      width: 150,
      fixed: 'right',
      ifShow: true,
    }];
    return columns;
  };
  return {
    getColumns,
    columns,
  };
}
