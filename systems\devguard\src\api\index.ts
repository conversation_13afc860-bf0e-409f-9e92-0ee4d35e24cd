import { requestService } from '../services/req.ts';
import type { PagedQueryParam, PermissionBaseRes } from './_common.ts';

export interface DepotsListItem {
  ID: number;
  prefix: string;
  depth?: number;
  isLocal?: boolean;
  serverID?: number;
  name?: string;
}

export interface StreamsListItem {
  ID?: number;
  CreatedAt?: string;
  UpdatedAt?: string;
  depotID?: number;
  name?: string;
  path?: string;
  description?: string;
  streamType?: number;
  rootDirectory?: string;
  workspace?: string;
  step?: number;
  userID?: number;
  sort?: number;
  submittingCount?: number;
  todaySubmittedCount?: number;
  instance?: {
    compile: boolean;
    status: number;
  }[];
  instanceCount?: number;
}
/**
 * 获取仓库列表
 */
export const getDepotsListByPage = requestService.GET<
  { projectID: number } & PagedQueryParam,
  Record<string, never>,
  PermissionBaseRes<{ list: DepotsListItem[] }>
>('/api/v1/projects/:projectID/depots');

/**
 * 获取主分支列表页信息
 */
export const getListSubmit = requestService.GET<
  { iid: number } & { deptPrefix: string },
  Record<string, never>,
  PermissionBaseRes<{ list: StreamsListItem[] }>
>('/api/v1/projects/:iid/submit/streams/listSubmit');
