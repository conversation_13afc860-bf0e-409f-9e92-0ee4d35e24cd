import { defineViteConfigProjectVueSys } from '@hg-tech/configs';
import UnoCSS from 'unocss/vite';
import process from 'node:process';
import svgLoader from 'vite-svg-loader';

// https://vitejs.dev/config/
export default defineViteConfigProjectVueSys(() => {
  return {
    plugins: [
      UnoCSS(),
      svgLoader(),
    ],
    server: {
      host: '0.0.0.0',
      port: 5500,
      proxy: {
        '/api': {
          ws: true,
          secure: false,
          changeOrigin: true,
          target: 'https://t-tech.int.hypergryph.com/api',
        },
      },
    },
  };
}, {
  analyze: process.argv.includes('--analyze'),
});
