/* eslint-disable no-console */
import type { SDKCarrier } from '@hg/event-log';
import type { UserInfoModel } from '/@/api/sys/model/userModel';
import { initWebJS } from '@hg/event-log';
import { isProdEnv } from '/@/utils/is';
import { findItemInMenuByPath, ModulesMenuConfig } from '@hg-tech/oasis-common';
import { merge } from 'lodash-es';

/**
 * 针对以下UA的访问，不再进行埋点上报
 */
const HEADLESS_UA_LIST: string[] = [
  'HeadlessChrome',
  'HgHeadlessBot',
];

interface CommonParams {
  /**
   * 前路由所属业务层级
   */
  business_level_prv: string;
  /**
   * 所属业务层级
   */
  business_level_cur: string;
  /**
   * 前route_name
   */
  prv_route_name?: string;
  /**
   * 当前route_name
   */
  route_name?: string;
  /**
   * 前路由路径
   */
  prv_route_path: string;
  /**
   * 当前路由路径
   */
  route_path: string;
  /**
   * 主题
   */
  theme: string;
  /**
   * 当前项目ID
   */
  project_id: string;
}

let webEventTracker: SDKCarrier;

let commonParams: CommonParams = {
  business_level_prv: '',
  business_level_cur: '',
  prv_route_name: '',
  route_name: '',
  prv_route_path: '',
  route_path: '',
  theme: '',
  project_id: '',
};

/**
 * 判断是否为无头浏览器
 */
function isHeadlessUA() {
  const ua = window.navigator.userAgent;
  return HEADLESS_UA_LIST.some((item) => ua.includes(item));
}

/**
 * 设置埋点通用参数
 */
export function setTrackCommonParams(params: Partial<CommonParams>) {
  commonParams = { ...commonParams, ...params };
}

function getTrackCommonParams() {
  return { ...commonParams };
}

// 当前仅在生产环境下使用埋点
if (isProdEnv) {
  /**
   * @see https://hypergryph.feishu.cn/wiki/PbPrw5HJMiLy05k2GOjceRL5nxc
   */
  webEventTracker = initWebJS({
    /**
     * @see https://datalake.hypergryph.net/area/11/app/app_manager/app_list_manager
     */
    appId: '7l6k0y2vw4rksevp6bibwuht',
    regionTag: 'cn',
    enableRealTimeSend: true,
  }) as SDKCarrier;
}

async function beforeLog() {
  const [{ store }, { useAppStore }, { useUserStore }] = await Promise.all([
    import('../../store'),
    import('../../store/modules/app'),
    import('../../store/modules/user'),
  ]);
  const appStore = useAppStore(store);
  const userStore = useUserStore(store);
  const { route_path, prv_route_path } = getTrackCommonParams();
  const { path: prvPath } = findItemInMenuByPath(ModulesMenuConfig, prv_route_path);
  const { path } = findItemInMenuByPath(ModulesMenuConfig, route_path);

  setTrackCommonParams({
    business_level_cur: path.join('/') || '',
    business_level_prv: prvPath.join('/'),
    route_name: path[path.length - 1],
    prv_route_name: prvPath[prvPath.length - 1] || '',
    theme: appStore.getDarkMode,
    project_id: userStore.getProjectId?.toString(),
  });

  return getTrackCommonParams();
}

async function sendLog(params: { name: 'login'; userId: string; eventData?: Record<string, any> }): Promise<void>;
async function sendLog(params: { name: 'pv'; eventData?: Record<string, any> }): Promise<void>;
async function sendLog(params: { name: string; eventData?: Record<string, any> }): Promise<void>;
async function sendLog(params: { name: 'login' | 'pv' | string; userId?: string; eventData?: Record<string, any> }): Promise<void> {
  try {
    const commonParams = await beforeLog();
    const sentData = merge({}, commonParams, params.eventData);
    if (!isProdEnv) {
      console.info(`[sendLog] ${params.name}埋点 params:`, sentData, {
        commonParams,
        params,
      });
    }

    if (isHeadlessUA()) {
      console.warn('当前为无头浏览器，埋点上报被忽略');
      return;
    }

    switch (params.name) {
      case 'pv':
        webEventTracker?.pageViewEvent?.(sentData);
        break;
      case 'login':
        webEventTracker?.userLoginEvent(params.userId || '', sentData);
        break;
      default:
        webEventTracker?.event(params.name, sentData);
        break;
    }
  } catch (e) {
    console.error('[sendLog]埋点失败：', e);
  }
}

/**
 * 更新埋点用户信息
 */
export function updateTrackerUserInfo(user: UserInfoModel | null) {
  try {
    if (user?.email) {
      webEventTracker?.setUser(user.email);
    } else {
      webEventTracker?.unsetUser();
    }
  } catch (e) {
    console.error('[traceUserLogin]埋点失败：', e);
  }
}

/**
 * 记录用户登录
 */
export function traceUserLogin(user?: UserInfoModel | null) {
  if (user?.email) {
    sendLog({ name: 'login', userId: user.email }).catch((e) => {
      console.error('[traceUserLogin]埋点失败：', e);
    });
  }
}

/**
 * 记录路由跳转
 */
export function traceRouteChange(params: {
  data: {
    route_name: string;
    route_path: string;
    prv_route_name?: string;
    prv_route_path?: string;
    [key: string]: any;
  };
  /**
   * 是否来自子系统
   */
  isFromMicroApp?: boolean;
}) {
  sendLog({
    name: 'pv',
    eventData: params.data,
  }).catch((e) => {
    console.error('[traceRouteChange]埋点失败：', e);
  });
}

export function sendEvent(eventName: string, eventData?: Record<string, any>) {
  sendLog({ name: eventName, eventData }).catch((e) => {
    console.error('[sendEvent]埋点失败：', e);
  });
}
