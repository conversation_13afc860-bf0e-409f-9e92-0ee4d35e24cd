import { UserOutlined } from '@ant-design/icons-vue';
import { Avatar, Select } from 'ant-design-vue';
import type { DefaultOptionType, SelectValue } from 'ant-design-vue/es/select';
import type { FilterFunc } from 'ant-design-vue/es/vc-select/Select';
import { debounce } from 'lodash';
import { type PropType, computed, defineComponent, ref } from 'vue';
import { toHighlightSegments } from '@hg-tech/utils';
import { pinyin } from 'pinyin-pro';

interface ForgeonUserSelectorOptionItem {
  id: string | number;
  name: string;
  nickName?: string;
  avatar?: string;
}

const ForgeonUserSelector = defineComponent({
  props: {
    value: {
      type: [String, Array, Number] as PropType<SelectValue>,
      default: () => [],
    },
    multiple: {
      type: Boolean as PropType<boolean>,
      default: false,
    },
    defaultValue: {
      type: Object as PropType<SelectValue>,
    },
    onSearch: {
      type: Function as PropType<(params: { query: string }, options: any) => void>,
      default: () => () => {},
    },
    onReset: {
      type: Function as PropType<() => void>,
      default: () => () => {},
    },
    options: {
      type: Array as PropType<ForgeonUserSelectorOptionItem[]>,
      default: () => [],
    },
    placeholder: {
      type: String as PropType<string>,
      default: '请选择用户',
    },
    loading: {
      type: Boolean as PropType<boolean>,
      default: false,
    },
    showAvatar: {
      type: Boolean as PropType<boolean>,
      default: false,
    },
    showSearch: {
      type: Boolean as PropType<boolean>,
      default: true,
    },
    filterOption: {
      type: [Boolean, Function] as PropType<boolean | FilterFunc<DefaultOptionType>>,
      default: false,
    },
    maxTagCount: {
      type: Number as PropType<number>,
      default: undefined,
    },
  },
  emits: ['update:value'],
  setup(props, { emit }) {
    const currentSearchText = ref<string>('');
    const userList = computed({
      get: () => {
        return props.value;
      },
      set: (value: string | string[]) => {
        emit('update:value', value);
      },
    });

    const charToSimplifiedAndPinyin = (char: string) => {
      const simp = char; // 假设已是简体
      const py = pinyin(char, { toneType: 'none', type: 'array' })[0] || char;
      const initial = py[0] || char;
      return [simp, py, initial];
    };

    const renderHighlightLabel = (text: string) => {
      return toHighlightSegments(text, currentSearchText.value, {
        transform: charToSimplifiedAndPinyin,
      }).map((segment, index) => (
        <span class={segment.highlight ? 'c-FO-Brand-Primary-Default' : ''} key={index}>
          {segment.text}
        </span>
      ));
    };

    return () => (
      <Select
        allowClear
        defaultValue={props.defaultValue}
        filterOption={props.filterOption}
        loading={props.loading}
        maxTagCount={props.maxTagCount}
        maxTagTextLength={8}
        mode={props.multiple ? 'multiple' : undefined}
        onFocus={props.onReset}
        onSearch={debounce((v) => {
          if (v) {
            currentSearchText.value = v;
            props.onSearch({ query: v }, {});
          } else {
            currentSearchText.value = '';
            props.onReset();
          }
        }, 300)}
        optionLabelProp="label"
        placeholder={props.placeholder}
        showSearch={props.showSearch}
        v-model:value={userList.value}
        virtual
      >
        {
          props.options.map((user) => (
            <Select.Option label={`${user.name}${user.nickName ? `(${user.nickName})` : ''}`} value={user.id}>
              <div class="flex items-center gap-8px">
                {props.showAvatar && (
                  <Avatar
                    class="forgeon-user-selector-avatar"
                    icon={user.avatar ? undefined : <UserOutlined />}
                    size={16}
                    src={user.avatar}
                  />
                )}
                <div>
                  {renderHighlightLabel(user.name)}
                  {renderHighlightLabel(user.nickName ? `(${user.nickName})` : '')}
                </div>
              </div>
            </Select.Option>
          ))
        }
      </Select>
    );
  },
});

export type { ForgeonUserSelectorOptionItem };
export { ForgeonUserSelector };
