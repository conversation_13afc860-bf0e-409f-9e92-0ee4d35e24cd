/**
 * 构建路由 URL
 * @param pathTemplate 路径模板，支持 :param 和正则参数
 * @param params 路径参数
 * @param query 查询参数
 */
function buildRouteUrl(
  pathTemplate: string,
  params: Record<string, any> = {},
  query?: Record<string, any>,
): string {
  // 替换 path 中的 :param 或正则参数
  let path = pathTemplate;

  const paramPattern = /:(\w+)(\([^)]*\))?([+*?])?/g;

  path = pathTemplate.replace(paramPattern, (_, key, _reg = '', modifier = '') => {
    const rawVal = params[key];

    if (rawVal == null) {
      if (modifier === '?' || modifier === '*') {
        return '';
      } // 可选参数省略
      throw new Error(`Missing required param "${key}" for path "${pathTemplate}"`);
    }

    const encode = (v: any) => encodeURIComponent(String(v));

    if (modifier === '+' || modifier === '*') {
      if (!Array.isArray(rawVal)) {
        throw new TypeError(`Param "${key}" should be an array for modifier "${modifier}"`);
      }
      return rawVal.map(encode).join('/');
    }

    // 单值参数
    return encode(rawVal as any);
  });

  // 拼接 query 字符串
  let queryString = '';
  if (query) {
    const searchParams = new URLSearchParams();
    for (const [key, val] of Object.entries(query)) {
      if (Array.isArray(val)) {
        for (const item of val) {
          if (item != null) {
            searchParams.append(key, String(item));
          }
        }
      } else if (val != null) {
        searchParams.append(key, String(val));
      }
    }
    const raw = searchParams.toString();
    if (raw) {
      queryString = `?${raw}`;
    }
  }

  return path + queryString;
}

export { buildRouteUrl };
