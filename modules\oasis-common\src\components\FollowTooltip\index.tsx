import { type CSSProperties, type PropType, defineComponent, onBeforeUnmount, ref } from 'vue';

const FollowTooltip = defineComponent({
  props: {
    content: {
      type: String as PropType<string>,
      default: '',
    },
    offset: {
      type: Number as PropType<number>,
      default: 0,
    },
    tooltipStyle: {
      type: Object as PropType<CSSProperties>,
      default: () => ({}),
    },
  },
  setup(props, { slots }) {
    const visible = ref(false);
    const tooltipX = ref(0);
    const tooltipY = ref(0);

    const wrapperRef = ref<HTMLElement | null>(null);

    let animationFrameId: number | null = null;

    const onMouseMove = (e: MouseEvent) => {
      if (animationFrameId) {
        return;
      }
      animationFrameId = requestAnimationFrame(() => {
        tooltipX.value = e.clientX + props.offset;
        tooltipY.value = e.clientY + props.offset;
        animationFrameId = null;
      });
    };

    const onMouseEnter = () => {
      visible.value = true;
      document.addEventListener('mousemove', onMouseMove);
    };

    const onMouseLeave = (e: MouseEvent) => {
      // 检查鼠标是否离开 wrapper 外部
      const target = e.relatedTarget as Node;
      if (wrapperRef.value && !wrapperRef.value.contains(target)) {
        visible.value = false;
        document.removeEventListener('mousemove', onMouseMove);
      }
    };

    onBeforeUnmount(() => {
      document.removeEventListener('mousemove', onMouseMove);
    });

    return () => (
      <div
        class="inline-block h-full w-full"
        onMouseenter={onMouseEnter}
        onMouseleave={onMouseLeave}
        ref={wrapperRef}
      >
        {slots.default?.()}

        {visible.value && (
          <div
            class="FO-Font-R12 pointer-events-none pos-fixed rd-4px bg-FO-Container-Fill2 p-12px c-FO-Content-Text1"
            style={{
              top: `${tooltipY.value}px`,
              left: `${tooltipX.value}px`,
              zIndex: 9999,
              transform: 'translateY(-50%)',
              ...props.tooltipStyle,
            }}
          >
            {slots.content ? slots.content() : props.content}
          </div>
        )}
      </div>
    );
  },
});

export { FollowTooltip };
