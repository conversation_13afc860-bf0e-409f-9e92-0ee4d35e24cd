<template>
  <div class="flex items-center gap-[24px] p-[20px]">
    List View
    <Button @click="onRouteToAIChat">
      Go to AI Chat with new tab
    </Button>
    <Button @click="onRouteToAIVersionNewsOriginData">
      去版本要闻详情
    </Button>
  </div>
  <div class="flex flex-col items-center gap-[20px] p-[20px]">
    <div>Gantt Base Demo</div>
    <GanttChart
      class="w-full rd-[12px] bg-FO-Container-Fill1"
      :height="200"
      :items="testGanttItems"
      :min="displayRange[0]"
      :max="displayRange[1]"
      :axisLabelFormat="(percentage) => {
        return dayjs(percentage).format('HH:mm');
      }"
      :tickInterval="tickInterval"
      :precision="60 * 1000 * 10"
      :onUpdate="onHandleItemUpdate"
      :renderItem="renderItem"
      :resizeable="true"
    />
    <Slider
      v-model:value="displayRange" class="w-full" :min="1719830400000" :max="1719916800000" range
      :tipFormatter="(percentage) => dayjs(percentage).format('YYYY-MM-DD HH:mm')"
    />
    <RangeSlider
      :min="1719830400000"
      :max="1719916800000"
      :value="displayRange"
      :onUpdateValue="onRangeChange"
      :precision="60 * 1000 * 5"
      :immediate="true"
    />
    <div>
      <div v-for="item in testGanttItems" :key="item.id">
        {{ item.label }}: {{ dayjs(item.start).format('HH:mm') }} - {{ dayjs(item.end).format('HH:mm') }} 长度: {{ item.end - item.start }}
      </div>
    </div>
  </div>
</template>

<script lang="tsx" setup>
import { type GanttItem, FollowTooltip, GanttChart, PlatformRoutePath, RangeSlider } from '@hg-tech/oasis-common';
import { useRouteNavigationStore } from '../store/modules/routeNavigation';
import { store } from '../store/pinia';
import { Button, Slider } from 'ant-design-vue';
import { computed, ref } from 'vue';
import dayjs from 'dayjs';

const navigationStore = useRouteNavigationStore(store);

const displayRange = ref<[number, number]>([1719830400000, 1719916800000]);
const tickInterval = computed(() => {
  const range = displayRange.value[1] - displayRange.value[0];
  // 以小时为单位，间隔为1小时或2小时（以毫秒为单位）
  const oneHour = 3600 * 1000;
  return range < 24 * oneHour ? oneHour : 2 * oneHour;
});
const testGanttItems = ref<GanttItem[]>([
  {
    id: '1',
    label: 'Task 1',
    start: 1719830400000, // 2024-07-01 00:00:00 GMT+8
    end: 1719916800000, // 2024-07-02 00:00:00 GMT+8
  },
  {
    id: '2',
    label: 'Task 2',
    start: 1719841200000, // 2024-07-01 03:00:00 GMT+8
    end: 1719862800000, // 2024-07-01 09:00:00 GMT+8
  },
  {
    id: '3',
    label: 'Task 3',
    start: 1719873600000, // 2024-07-01 12:00:00 GMT+8
    end: 1719916800000, // 2024-07-02 00:00:00 GMT+8
  },
  {
    id: '4',
    label: 'Task 4',
    start: 1719873600000, // 2024-07-01 00:00:00 GMT+8
    end: 1719916800000, // 2024-07-02 00:00:00 GMT+8
  },
  {
    id: '5',
    label: 'Task 5',
    start: 1719830400000, // 2024-07-01 00:00:00 GMT+8
    end: 1719862800000, // 2024-07-02 00:00:00 GMT+8
  },
]);

function onRouteToAIChat() {
  navigationStore.onRouteNavigate(PlatformRoutePath.AIChat, {}, {}, { openInNewTab: true });
}

function onRouteToAIVersionNewsOriginData() {
  navigationStore.onRouteNavigate(PlatformRoutePath.VersionNewsOriginData, {
    taskId: '1945075622062530560',
    secondSummaryId: '1752577500889988',
    summaryId: '1752577500904933',
  }, {
    content: '这是一个测试内容',
    secondContent: '这是一个二级测试内容',
  }, { openInNewTab: false });
}

function onHandleItemUpdate(item: GanttItem, start: number, end: number) {
  const targetItem = testGanttItems.value.find((i) => i.id === item.id);
  if (targetItem) {
    targetItem.start = start;
    targetItem.end = end;
  }
}

function renderItem(item: GanttItem) {
  return (
    <FollowTooltip offset={-50} tooltipStyle={{ minWidth: '120px' }}>
      {{
        default: () => (
          <div class="h-full w-full flex items-center justify-center">
            <span class="text-FO-Text-Primary text-[12px]">{item.label}</span>
          </div>
        ),
        content: () => (
          <div>
            <div class="text-FO-Text-Primary text-[12px]">{item.label}</div>
            <div class="text-FO-Text-Secondary text-[12px]">
              {dayjs(item.start).format('HH:mm')} - {dayjs(item.end).format('HH:mm')}
            </div>
            <div class="text-FO-Text-Secondary text-[12px]">长度: {item.end - item.start} ms</div>
          </div>
        ),
      }}
    </FollowTooltip>
  );
}
function onRangeChange(value: [number, number]) {
  displayRange.value = value;
}
</script>
