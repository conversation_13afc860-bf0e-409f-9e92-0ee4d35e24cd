import { createRouter, createWebHistory } from 'vue-router';
import type { App } from 'vue';
import { withDocumentTitle } from './modules/withDocumentTitle.ts';
import { withTrack } from './modules/withTrack.ts';
import { PlatformEnterPoint, PlatformRoutePath } from '@hg-tech/oasis-common';

export const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      name: PlatformEnterPoint.CommitCenter,
      path: PlatformRoutePath.CommitCenter,
      component: () => import('../views/commit-center-home/List.vue'),
    },
  ],
  strict: true,
  scrollBehavior: () => ({ left: 0, top: 0 }),
});

export function setupRouter(app: App<Element>) {
  app.use(router);

  // with plugins
  withDocumentTitle(router);
  withTrack(router);
}
