import { describe, expect, it } from 'vitest';
import { buildRouteUrl } from './combine-url';

describe('buildRouteUrl', () => {
  it('替换路径params并拼接query', () => {
    const pathTemplate = '/user/:id/details';
    const params = { id: 123 };
    const query = { search: 'test', page: 2 };

    const result = buildRouteUrl(pathTemplate, params, query);
    expect(result).toBe('/user/123/details?search=test&page=2');
  });
  it('应该处理可选params', () => {
    const pathTemplate = '/user/:id?';
    const params1 = { id: 123 };
    const params2 = {};

    const result1 = buildRouteUrl(pathTemplate, params1);
    const result2 = buildRouteUrl(pathTemplate, params2);
    expect(result1).toBe('/user/123');
    expect(result2).toBe('/user/');
  });
  it('应该处理空query', () => {
    const pathTemplate = '/user/:id/details';
    const params = { id: 123 };

    const result = buildRouteUrl(pathTemplate, params);
    expect(result).toBe('/user/123/details');
  });
  it('应该抛出错误当缺少必需的params', () => {
    const pathTemplate = '/user/:id/details';
    const params = {};
    expect(() => buildRouteUrl(pathTemplate, params)).toThrowError('Missing required param');
  });
  it('应该正确处理特殊字符', () => {
    const pathTemplate = '/search/:query';
    const params = { query: 'test+search' };
    const result = buildRouteUrl(pathTemplate, params);
    expect(result).toBe('/search/test%2Bsearch');
  });
  it('应该处理数组query参数', () => {
    const pathTemplate = '/items';
    const params = {};
    const query = { tags: ['tag1', 'tag2'] };
    const result = buildRouteUrl(pathTemplate, params, query);
    expect(result).toBe('/items?tags=tag1&tags=tag2');
  });
  it('非正则path应当不匹配正则参数', () => {
    const pathTemplate = '/user/details';
    const params = { id: '123' };
    const query = { details: 'extra' };
    const result = buildRouteUrl(pathTemplate, params, query);
    expect(result).toBe('/user/details?details=extra');
  });
  it('常见vuerouter的正则path应当正确处理', () => {
    const pathTemplate = '/files/:path+';
    const pathTemplate1 = '/files/:path*';
    const params = { path: ['user', '123'] };
    const result = buildRouteUrl(pathTemplate, params);
    const result1 = buildRouteUrl(pathTemplate1, params);
    expect(result).toBe('/files/user/123');
    expect(result1).toBe('/files/user/123');
  });
});
