import { type <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Divider, Drawer, message } from 'ant-design-vue';
import { type PropType, computed, defineComponent, nextTick, ref, watch } from 'vue';
import Icon from '@ant-design/icons-vue';
import { type MergeAdvancedRuleForm, type MergeBasicRuleForm, MergeRuleFormPanelKey } from '../../../models/config.model';
import type { Rule } from 'ant-design-vue/es/form';
import { useMergeHome } from '../use-merge-home';
import type { RuleV1Rule } from '@hg-tech/api-schema-merge';
import { BasicForm } from './basic-form';
import { AdvancedForm } from './advanced-form';
import { useLatestPromise } from '@hg-tech/utils-vue';
import { mergeApi } from '../../../api';

import Close from '../../../assets/svg/Close.svg?component';

const MergeConfigDrawer = defineComponent({
  props: {
    id: {
      type: String as PropType<string>,
      default: '',
    },
    visible: {
      type: Boolean as PropType<boolean>,
      default: false,
    },
    defaultForm: {
      type: Object as PropType<RuleV1Rule>,
    },
  },
  emits: ['update:visible'],
  setup(props, { emit }) {
    const isEdit = computed(() => !!props.id);
    const drawerTitle = computed(() => (`${isEdit.value ? '编辑' : '新增'}自动合并配置`));
    const basicFormRef = ref<FormInstance>();
    const advancedFormRef = ref<FormInstance>();
    const defaultActive = ref([MergeRuleFormPanelKey.Default]);
    const advancedActive = ref([MergeRuleFormPanelKey.Advanced]);
    const { currentProjectId, streamList, onCreateRule, onUpdateRule, onUpdateDefaultForm, currentBranchMap } = useMergeHome();
    const { data: defaultTagsRes, execute: getTags } = useLatestPromise(mergeApi.v1.ruleServiceGetTags);
    const defaultTagOptions = computed(() => defaultTagsRes.value?.data?.data?.tags?.map((tag) => ({ label: tag, value: tag })) ?? []);

    const basicForm = ref<MergeBasicRuleForm>({
      id: undefined,
      depot: undefined,
      sourceStream: undefined,
      targetStream: undefined,
      sourceStreamId: undefined,
      targetStreamId: undefined,
      mergeTrigger: undefined,
      triggerTags: [],
    });

    const advancedForm = ref<MergeAdvancedRuleForm>({
      initialCl: undefined,
      excludeUsers: [] as string[],
      defaultResolvers: [] as string[],
      defaultResolveRule: undefined,
      ignoredTags: [],
      enableFeishuComment: false,
      maxFiles: 1000,
      useCommitTool: false,
      autoRetryEnable: false,
      retryIntervalMinute: undefined,
      excludeRules: [{
        id: Math.random().toString(36).substring(2, 15),
        ruleType: undefined,
        pattern: '',
      }],
      customResolveRules: [{
        id: Math.random().toString(36).substring(2, 15),
        ruleType: undefined,
        pattern: '',
        resolveRule: undefined,
      }],
      excludeUserUsers: [],
      defaultResolverUsers: [],
      onlineProcessFileTypes: [],
      updateSubmitter: false,
    });

    const open = computed({
      get: () => props.visible,
      set: (value) => {
        emit('update:visible', value);
      },
    });

    const rules: Record<string, Rule[]> = {
      sourceStreamId: [
        { required: true, message: '请选择源分支' },
        {
          validator(_: unknown, value: number) {
            if (value && value === basicForm.value.targetStreamId) {
              return Promise.reject(new Error('源分支和目标分支不能相同'));
            }
            basicFormRef.value?.clearValidate(['targetStreamId']);
            return Promise.resolve();
          },
        },
        {
          validator(_: unknown, value: number) {
            const sourceGroup = streamList.value.find((dep) => dep.streams?.some((s) => s.id === value));
            const targetGroup = streamList.value.find((dep) => dep.streams?.some((s) => s.id === basicForm.value.targetStreamId));
            if (value && basicForm.value.targetStreamId && sourceGroup && targetGroup && sourceGroup.path !== targetGroup.path) {
              return Promise.reject(new Error('源分支和目标分支必须属于同一个仓库下'));
            }
            basicFormRef.value?.clearValidate(['targetStreamId']);
            return Promise.resolve();
          },
        },
      ],
      targetStreamId: [
        { required: true, message: '请选择目标分支' },
        {
          validator(_: unknown, value: number) {
            if (value && value === basicForm.value.sourceStreamId) {
              return Promise.reject(new Error('源分支和目标分支不能相同'));
            }
            basicFormRef.value?.clearValidate(['sourceStreamId']);
            return Promise.resolve();
          },
        },
        {
          validator(_: unknown, value: number) {
            const sourceGroup = streamList.value.find((dep) => dep.streams?.some((s) => s.id === basicForm.value.sourceStreamId));
            const targetGroup = streamList.value.find((dep) => dep.streams?.some((s) => s.id === value));
            if (value && basicForm.value.sourceStreamId && sourceGroup && targetGroup && sourceGroup.path !== targetGroup.path) {
              return Promise.reject(new Error('源分支和目标分支必须属于同一个仓库下'));
            }
            basicFormRef.value?.clearValidate(['sourceStreamId']);
            return Promise.resolve();
          },
        },
      ],
      mergeTrigger: [
        { required: true, message: '请选择触发方式' },
      ],
      triggerTags: [
        { required: true, message: '请输入触发标签' },
      ],
      defaultResolveRule: [
        { required: true, message: '请选择默认合并规则' },
      ],
      maxFiles: [
        { required: true, message: '请输入文件合并最大数量' },
        { type: 'number', min: 1, max: 10000, message: '请输入1-10000的数字' },
      ],
      retryIntervalMinute: [
        { required: true, message: '请选择自动重试时间' },
      ],
      tag: [
        { required: true, message: '请输入不合并的标签' },
      ],
      description: [
        { required: true, message: '请输入不合并的标签描述' },
      ],
    };

    watch(() => open.value, (newVal) => {
      if (newVal) {
        getTags({ id: currentProjectId.value!, streamPath: basicForm.value.sourceStreamId ? currentBranchMap.value.get(basicForm.value.sourceStreamId)?.path || '' : '' }, {});
        nextTick(() => {
          defaultActive.value = [MergeRuleFormPanelKey.Default];
          advancedActive.value = [];
          !isEdit.value && (basicForm.value = {
            id: undefined,
            depot: undefined,
            sourceStream: undefined,
            targetStream: undefined,
            mergeTrigger: undefined,
            triggerTags: undefined,
          });
        });
      }
    }, { immediate: true });

    watch([() => props.defaultForm, open], (newVal) => {
      const [newForm, _] = newVal;
      if (newForm && isEdit.value) {
        basicForm.value = {
          id: newForm.id,
          depot: newForm.depot,
          sourceStream: newForm.sourceStream,
          targetStream: newForm.targetStream,
          sourceStreamId: newForm.sourceStreamId,
          targetStreamId: newForm.targetStreamId,
          mergeTrigger: newForm.mergeTrigger,
          triggerTags: newForm.triggerTags,
        };
        advancedForm.value = {
          initialCl: Number(newForm.initialCl) > 0 ? newForm.initialCl : undefined,
          excludeUsers: newForm.excludeUsers || [],
          defaultResolvers: newForm.defaultResolvers || [],
          defaultResolveRule: newForm.defaultResolveRule,
          ignoredTags: newForm.ignoredTags,
          enableFeishuComment: newForm.enableFeishuComment,
          maxFiles: newForm.maxFiles,
          useCommitTool: newForm.useCommitTool,
          autoRetryEnable: newForm.autoRetryEnable,
          retryIntervalMinute: newForm.retryIntervalMinute,
          excludeRules: newForm.excludeRules?.map((item) => ({
            ...item,
            id: Math.random().toString(36).substring(2, 15),
          })) || [{
            id: Math.random().toString(36).substring(2, 15),
            ruleType: undefined,
            pattern: '',
          }],
          customResolveRules: newForm.customResolveRules?.map((item) => ({
            ...item,
            id: Math.random().toString(36).substring(2, 15),
          })) || [{
            id: Math.random().toString(36).substring(2, 15),
            ruleType: undefined,
            pattern: '',
            resolveRule: undefined,
          }],
          excludeUserUsers: newForm.excludeUserUsers || [],
          defaultResolverUsers: newForm.defaultResolverUsers || [],
          onlineProcessFileTypes: newForm.onlineProcessFileTypes || [],
          updateSubmitter: newForm.updateSubmitter,
        };
      } else {
        basicForm.value = {
          id: undefined,
          depot: undefined,
          sourceStream: undefined,
          targetStream: undefined,
          mergeTrigger: undefined,
          triggerTags: [],
        };
        advancedForm.value = {
          initialCl: undefined,
          excludeUsers: [] as string[],
          defaultResolvers: [] as string[],
          defaultResolveRule: undefined,
          ignoredTags: [],
          enableFeishuComment: false,
          maxFiles: 1000,
          useCommitTool: false,
          autoRetryEnable: false,
          retryIntervalMinute: undefined,
          excludeRules: [{
            id: Math.random().toString(36).substring(2, 15),
            ruleType: undefined,
            pattern: '',
          }],
          customResolveRules: [{
            id: Math.random().toString(36).substring(2, 15),
            ruleType: undefined,
            pattern: '',
            resolveRule: undefined,
          }],
          excludeUserUsers: [],
          defaultResolverUsers: [],
          onlineProcessFileTypes: [],
          updateSubmitter: false,
        };
      }
    }, { deep: true, immediate: true });

    // 监听源分支变化，获取对应的标签
    watch(() => basicForm.value.sourceStreamId, (newVal, oldVal) => {
      if (newVal && newVal !== oldVal) {
        getTags({ id: currentProjectId.value!, streamPath: currentBranchMap.value.get(newVal)?.path || '' }, {});
      }
    });

    const onUpdateDefaultValue = () => {
      advancedFormRef.value?.validate().then(async () => {
        const params: RuleV1Rule = {
          ...advancedForm.value,
          ignoredTags: advancedForm.value.ignoredTags?.map((tag) => tag?.trim()).filter((tag) => tag),
          excludeRules: advancedForm.value.excludeRules?.map((item) => ({
            ...item,
            pattern: item.pattern?.trim(),
          })),
          customResolveRules: advancedForm.value.customResolveRules?.map((item) => ({
            ...item,
            pattern: item.pattern?.trim(),
          })),
          retryIntervalMinute: advancedForm.value.autoRetryEnable ? advancedForm.value.retryIntervalMinute : undefined,
        };
        const res = await onUpdateDefaultForm(params);
        if (res) {
          message.success('保存默认配置成功');
        }
      }, () => {
        console.error('Validation failed:');
        advancedActive.value = [MergeRuleFormPanelKey.Advanced];
        message.warning('存在未填写的必填项');
      });
    };

    const onClose = () => {
      open.value = false;
    };

    const onSubmit = () => {
      Promise.all([
        basicFormRef.value?.validate(),
        advancedFormRef.value?.validate(),
      ]).then(
        async () => {
          const depot = streamList.value.find((dep) =>
            dep.streams?.some((s) => s.id === basicForm.value.sourceStreamId),
          )?.path;
          const params: RuleV1Rule = {
            ...basicForm.value,
            ...advancedForm.value,
            sourceStream: basicForm.value.sourceStreamId ? currentBranchMap.value.get(basicForm.value.sourceStreamId)?.path : '',
            targetStream: basicForm.value.targetStreamId ? currentBranchMap.value.get(basicForm.value.targetStreamId)?.path : '',
            depot,
            ignoredTags: advancedForm.value.ignoredTags?.map((tag) => tag?.trim()).filter((tag) => tag),
            excludeRules: advancedForm.value.excludeRules?.map((item) => ({
              ...item,
              pattern: item.pattern?.trim(),
            })),
            customResolveRules: advancedForm.value.customResolveRules?.map((item) => ({
              ...item,
              pattern: item.pattern?.trim(),
            })),
            retryIntervalMinute: advancedForm.value.autoRetryEnable
              ? advancedForm.value.retryIntervalMinute
              : undefined,
          };
          const res = isEdit.value
            ? await onUpdateRule(params)
            : await onCreateRule(params);
          if (res) {
            message.success(isEdit.value ? '编辑成功' : '新增成功');
            onClose();
          }
        },
        (err) => {
          // 判断是哪个表单未通过校验
          if (err?.errorFields?.some((f: any) => Object.keys(basicForm.value).includes(f.name[0]))) {
            defaultActive.value = [MergeRuleFormPanelKey.Default];
            basicFormRef.value?.scrollToField(err.errorFields[0].name[0]);
          } else {
            advancedActive.value = [MergeRuleFormPanelKey.Advanced];
            advancedFormRef.value?.scrollToField(err.errorFields[0].name[0]);
          }
          message.warning('存在未填写的必填项');
        },
      );
    };

    return () => (
      <Drawer
        bodyStyle={{ padding: '8px', paddingTop: '12px' }}
        closable={false}
        destroyOnClose={true}
        mask={true}
        maskClosable={false}
        onClose={onClose}
        placement="right"
        title={drawerTitle.value}
        v-model:open={open.value}
        width={924}
      >
        {{
          extra: () => (
            <Button
              class="flex items-center justify-center"
              icon={(
                <Icon class="font-size-18px" component={<Close />} />
              )}
              onClick={onClose}
              type="text"
            />
          ),
          default: () => (
            <>
              <BasicForm
                formRef={basicFormRef}
                isEdit={isEdit.value}
                rules={rules}
                tagOptions={defaultTagOptions.value}
                v-model:activeKey={defaultActive.value}
                v-model:form={basicForm.value}
              />
              <Divider class="my-8px" />
              <AdvancedForm
                formRef={advancedFormRef}
                isEdit={isEdit.value}
                onCollapseBasic={() => defaultActive.value = []}
                onResetValidate={() => {
                  advancedFormRef.value?.validate();
                }}
                onSaveDefault={onUpdateDefaultValue}
                rules={rules}
                tagOptions={defaultTagOptions.value}
                v-model:activeKey={advancedActive.value}
                v-model:form={advancedForm.value}
              />
            </>
          ),
          footer: () => (
            <div class="flex justify-end gap-12px">
              <Button class="btn-fill-default" onClick={onClose} type="text">取消</Button>
              <Button class="btn-fill-primary" onClick={onSubmit} type="primary">保存</Button>
            </div>
          ),
        }}
      </Drawer>
    );
  },
});

export {
  MergeConfigDrawer,
};
