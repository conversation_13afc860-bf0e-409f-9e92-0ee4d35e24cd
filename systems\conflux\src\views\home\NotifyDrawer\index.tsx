import { type <PERSON>Inst<PERSON>, Button, Drawer, Form, message, Tooltip } from 'ant-design-vue';
import { type PropType, computed, defineComponent, nextTick, onMounted, reactive, ref, watch } from 'vue';
import Icon from '@ant-design/icons-vue';
import { useLatestPromise } from '@hg-tech/utils-vue';
import { mergeApi } from '../../../api';
import { useForgeonConfigStore } from '../../../store/modules/forgeonConfig';
import { store } from '../../../store/pinia';
import { useSortable } from '@vueuse/integrations/useSortable';
import type { SortableEvent } from 'sortablejs';
import { type ApiNotifyV1NotifyRule, ApiNotifyV1NotifyScheduleType, ApiNotifyV1NotifyTargetType } from '@hg-tech/api-schema-merge';
import { NotifyFormItem } from './NotifyFormItem';

import Close from '../../../assets/svg/Close.svg?component';
import SystemFillInfo from '../../../assets/svg/SystemFillInfo.svg?component';
import BasicStrokeHelp from '../../../assets/svg/BasicStrokeHelp.svg?component';
import Add from '../../../assets/svg/Add.svg?component';
import BasicStrokeProcessing from '../../../assets/svg/BasicStrokeProcessing.svg?component';
import DragStyle from '../../../components/Dragable.module.less';

const NotifyDrawer = defineComponent({
  props: {
    visible: {
      type: Boolean as PropType<boolean>,
      default: false,
    },
    onUpdateVisible: {
      type: Function as PropType<(value: boolean) => void>,
      default: () => {},
    },
  },
  setup(props) {
    const forgeonConfig = useForgeonConfigStore(store);
    const sortableContainer = ref<HTMLElement>();
    const { data: notifyRuleResponse, execute: fetchNotifyRule } = useLatestPromise(mergeApi.v1.notifyGetNotifyRule);
    const { data: chatListResponse, execute: fetchChatList } = useLatestPromise(mergeApi.v1.notifyGetChats);
    const originNotifyRule = ref<Array<ApiNotifyV1NotifyRule>>([]);
    const formRef = ref<FormInstance>();
    const form = reactive<{
      notifyRules: Array<ApiNotifyV1NotifyRule>;
    }>({
      notifyRules: [],
    });
    const chatOptions = computed(() => {
      return chatListResponse.value?.data?.data?.chats?.map((i) => ({
        id: i.chatId ?? '',
        name: i.name ?? '',
        avatar: i.avatar ?? '',
      })) ?? [];
    });

    const open = computed({
      get: () => props.visible,
      set: (value) => {
        props.onUpdateVisible(value);
      },
    });

    const refreshNotifyRule = async () => {
      await fetchNotifyRule({ id: forgeonConfig.currentProjectId ?? 0 }, {});
      originNotifyRule.value = notifyRuleResponse.value?.data?.data?.notifyRules || [];
      form.notifyRules = originNotifyRule.value.slice();
    };
    const refreshChatList = async () => {
      await fetchChatList({ id: forgeonConfig.currentProjectId ?? 0 }, {});
    };
    const onRefreshChatList = () => {
      refreshChatList();
      message.success('群组已更新');
    };

    const onCreateNotifyRule = () => {
      form.notifyRules.push({
        id: undefined,
        target: undefined,
        notifySchedule: {
          notifyScheduleType: ApiNotifyV1NotifyScheduleType.DAILY,
          hour: 0,
          minute: 0,
        },
        notifyTargetType: ApiNotifyV1NotifyTargetType.USER,
        enable: true,
      });
      nextTick(() => {
        formRef.value?.scrollToField(['notifyRules', form.notifyRules.length - 1, 'target']);
      });
    };

    const onClose = () => {
      open.value = false;
    };

    const onSubmit = async () => {
      formRef.value?.validate().then(async () => {
        const params = { notifyRules: form.notifyRules.map((item, index) => ({ ...item, sort: index + 1 })) };
        await mergeApi.v1.notifyUpdateNotifyRule({ id: forgeonConfig.currentProjectId ?? 0 }, params);
        message.success('保存成功');
        open.value = false;
        await refreshNotifyRule();
      }).catch((err) => {
        formRef.value?.scrollToField(err.errorFields[0].name);
      });
    };

    onMounted(() => {
      refreshChatList();
    });

    watch(() => props.visible, async (value) => {
      if (value) {
        refreshNotifyRule();
        nextTick(() => {
          useSortable(sortableContainer, form.notifyRules, {
            handle: '.drag-handle',
            animation: 300,
            direction: 'vertical',
            dragClass: DragStyle.dragGhost,
            ghostClass: DragStyle.dragGhost,
            onEnd: async (evt: SortableEvent) => {
              const { oldIndex, newIndex } = evt;
              if (oldIndex === newIndex || oldIndex === undefined || newIndex === undefined) {
                return;
              }
              const movedItem = form.notifyRules.splice(oldIndex, 1)[0];
              form.notifyRules.splice(newIndex, 0, movedItem);
              formRef.value?.validate();
            },
          });
        });
      }
    });

    return () => (
      <Drawer
        bodyStyle={{ padding: '8px', paddingTop: '12px' }}
        closable={false}
        destroyOnClose={false}
        mask={true}
        maskClosable={false}
        onClose={onClose}
        placement="right"
        title={(
          <>
            <span class="FO-Font-B16 c-FO-Content-Text1">配置任务通知</span>
            <Tooltip
              class="ml-8px"
              title="点击查看配置任务通知操作方式"
            >
              <Icon class="font-size-16px" component={<BasicStrokeHelp />} onClick={() => window.open('https://hypergryph.feishu.cn/wiki/H21bwPb7qiu1trkydxScft6Sn1d', '_blank')} />
            </Tooltip>
          </>
        )}
        v-model:open={open.value}
        width={924}
      >
        {{
          extra: () => (
            <Button
              class="flex items-center justify-center"
              icon={(
                <Icon class="font-size-18px" component={<Close />} />
              )}
              onClick={onClose}
              type="text"
            />
          ),
          default: () => (
            <div class="px-16px py-12px">
              <div
                class="mb-24px flex items-center gap-8px rd-6px bg-FO-Functional-Info2-Default px-12px py-8px c-FO-Content-Text1"
              >
                <Icon class="font-size-16px c-FO-Functional-Info1-Default" component={<SystemFillInfo />} />
                定时将待处理的任务统计信息发送到配置的用户/群组（配置群组前需先将 Conflux 机器人拉入群）
              </div>

              <div class="content">
                <div class="header flex items-center justify-between">
                  <div class="FO-Font-B16 c-FO-Content-Text1">配置列表</div>
                  <div class="actions flex items-center gap-8px">
                    <Button class="btn-fill-text" onClick={onRefreshChatList}>
                      <span class="FO-Font-B14 c-FO-Brand-Primary-Default">
                        <Icon class="mr-8px font-size-16px" component={<BasicStrokeProcessing />} />
                        更新群组
                      </span>

                    </Button>
                    <Button
                      class="btn-fill-secondary"
                      icon={<Icon class="font-size-16px" component={<Add />} />}
                      onClick={onCreateNotifyRule}
                    >
                      新建配置
                    </Button>
                  </div>
                </div>
                <Form class="mt-12px" model={form} ref={formRef}>
                  <div ref={sortableContainer}>
                    {
                      form.notifyRules.length
                        ? form.notifyRules.map((item, index) => (
                          <div class="w-full flex gap-12px" key={`${index}-${item.id}`}>
                            <NotifyFormItem
                              chatOptions={chatOptions.value}
                              content={item}
                              domain="notifyRules"
                              index={index}
                              onDelete={(index) => {
                                form.notifyRules.splice(index, 1);
                              }}
                              onUpdateContent={(value) => {
                                form.notifyRules[index] = value;
                              }}
                              originContent={originNotifyRule.value.find((i) => i.id === item.id)}
                            />
                          </div>
                        ))
                        : (
                          <div class="h-100px w-full flex items-center justify-center gap-12px">
                            <span class="c-FO-Content-Text2">暂无配置，点击右侧新建配置按钮创建</span>
                          </div>
                        )
                    }
                  </div>
                </Form>
              </div>
            </div>
          ),
          footer: () => (
            <div class="flex justify-end gap-12px">
              <Button class="btn-fill-default" onClick={onClose} type="text">取消</Button>
              <Button class="btn-fill-primary" onClick={onSubmit} type="primary">保存</Button>
            </div>
          ),
        }}
      </Drawer>
    );
  },
});

export {
  NotifyDrawer,
};
