import { createSharedComposable } from '@vueuse/core';
import { useForgeonConfigStore } from '../../store/modules/forgeonConfig';
import { store } from '../../store/pinia';
import { useLatestPromise } from '@hg-tech/utils-vue';
import { mergeApi } from '../../api';
import type { RuleV1Depot, RuleV1Rule, RuleV1Stream } from '@hg-tech/api-schema-merge';
import { computed } from 'vue';

const useMergeHome = createSharedComposable(() => {
  const forgeonConfig = useForgeonConfigStore(store);
  const currentProjectId = computed(() => forgeonConfig.currentProjectId);
  const { data: ruleResponse, loading: ruleListLoading, execute: getRuleList } = useLatestPromise(mergeApi.v1.ruleServiceGetRules);
  const { data: defaultFormRes, execute: getDefaultForm } = useLatestPromise(mergeApi.v1.ruleServiceGetDefaultRule);
  const { data: updateDefaultFormRes, execute: updateDefaultForm } = useLatestPromise(mergeApi.v1.ruleServiceUpdateDefaultRule);
  const { data: streamResponse, execute: getStreamList } = useLatestPromise(mergeApi.v1.ruleServiceGetStreams);
  const { data: createRuleRes, execute: createRule } = useLatestPromise(mergeApi.v1.ruleServiceCreateRule);
  const { data: updateRuleRes, execute: updateRule } = useLatestPromise(mergeApi.v1.ruleServiceUpdateRule);
  const { data: deleteRuleRes, execute: deleteRule } = useLatestPromise(mergeApi.v1.ruleServiceRemoveRule);
  const { execute: setRuleEnable } = useLatestPromise(mergeApi.v1.ruleServiceUpdateRuleEnable);

  const currentBranchMap = computed(() => {
    const depots = (streamResponse.value?.data?.data?.depots ?? []) as RuleV1Depot[];
    const map = new Map<number, RuleV1Stream & { depot: string }>();
    depots.forEach((depot) => {
      depot.streams?.forEach((stream) => {
        if (!stream.id) {
          return;
        }
        map.set(stream.id, { ...stream, depot: depot.name! });
      });
    });
    return map;
  });

  const initHomeData = async () => {
    if (!currentProjectId.value) {
      return;
    }
    await Promise.all([
      getRuleList({ id: currentProjectId.value }, {}),
      getDefaultForm({ id: currentProjectId.value }, {}),
      getStreamList({ id: currentProjectId.value }, {}),
    ]);
  };

  function refreshRuleList() {
    if (!currentProjectId.value) {
      return;
    }
    return getRuleList({ id: currentProjectId.value }, {});
  }

  const onUpdateDefaultForm = async (params: RuleV1Rule) => {
    if (!currentProjectId.value) {
      return;
    }
    await updateDefaultForm({ id: currentProjectId.value }, {
      rule: params,
    });
    if (updateDefaultFormRes.value?.data?.code === 0) {
      await initHomeData();
      return true;
    }
    return false;
  };

  const onCreateRule = async (params: RuleV1Rule): Promise<boolean> => {
    if (!currentProjectId.value) {
      return false;
    }
    await createRule({ id: currentProjectId.value }, {
      rule: params,
    });
    if (createRuleRes.value?.data?.code === 0) {
      await getRuleList({ id: currentProjectId.value }, {});
      return true;
    }
    return false;
  };

  const onUpdateRule = async (params: RuleV1Rule): Promise<boolean> => {
    if (!currentProjectId.value || !params.id) {
      return false;
    }
    await updateRule({ id: currentProjectId.value, ruleId: params.id }, {
      rule: params,
    });
    if (updateRuleRes.value?.data?.code === 0) {
      await getRuleList({ id: currentProjectId.value }, {});
      return true;
    }
    return false;
  };

  const onDeleteRule = async (ruleId: string) => {
    if (!currentProjectId.value) {
      return;
    }
    await deleteRule({ id: currentProjectId.value, ruleId }, {});
    if (deleteRuleRes.value?.data?.code === 0) {
      await getRuleList({ id: currentProjectId.value }, {});
    }
  };

  const onSetRuleEnable = async (ruleId: string, enable: boolean) => {
    if (!currentProjectId.value) {
      return;
    }
    await setRuleEnable({ id: currentProjectId.value, ruleId }, {
      enable,
    });
    await getRuleList({ id: currentProjectId.value }, {});
  };

  return {
    streamList: computed(() => streamResponse.value?.data?.data?.depots ?? []),
    defaultFormRule: computed(() => defaultFormRes.value?.data?.data?.rule),
    currentProjectId,
    ruleList: computed(() => ruleResponse.value?.data?.data?.rules ?? []),
    ruleListLoading,
    currentBranchMap,
    initHomeData,
    onUpdateDefaultForm,
    onCreateRule,
    refreshRuleList,
    onUpdateRule,
    onDeleteRule,
    onSetRuleEnable,
  };
});

export {
  useMergeHome,
};
