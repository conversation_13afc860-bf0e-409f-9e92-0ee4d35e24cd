<template>
  <PageWrapper :class="prefixCls" :title="`CL标签: ${streamName}`" @back="goBack">
    <template #subTitle>
      <div class="ml-3 mt-2 text-xs">
        CL已标记的changelist标签，其他工具可依据changelist号获取这些标签。点击标签可删除标签。
      </div>
    </template>
    <template #extra>
      <a-button
        v-track="'irinrivrqw'"
        size="small"
        shape="round"
        class="!text-xs !font-bold"
        @click="handleCreateClLabel()"
      >
        添加CL标签
      </a-button>
      <a-button
        v-track="'omcdsljwlk'"
        size="small"
        shape="round"
        class="!text-xs !font-bold"
        @click="handleLabelConfig()"
      >
        标签配置
      </a-button>
    </template>
    <div class="mb-4 flex items-center rounded-md bg-FO-Container-Fill1 p-3">
      <div class="font-bold">
        筛选：
      </div>
      <a-input
        v-model:value="clNumber"
        placeholder="请输入CL号查询"
        class="w-300px"
        type="number"
        min="0"
      />
      <a-button size="small" shape="round" class="ml-2 !text-xs !font-bold" @click="handleClear">
        清空筛选
      </a-button>
    </div>

    <div v-if="clRecordList?.length" class="h-[calc(100vh_-_274px)] overflow-auto">
      <div
        v-for="item in clRecordList"
        :key="item.ID"
        class="mt-4 flex justify-between gap-4 rounded-md bg-FO-Container-Fill1 px-6 py-3 first:mt-0"
      >
        <div class="self-center pl-2 pr-4 text-center font-size-24px font-bold">
          {{ item.cl }}
        </div>
        <div class="w-200px flex flex-col gap-4">
          <div class="flex items-center">
            <b>日期：</b>
            <EllipsisText class="flex-1">
              {{ formatTISOToDate(item.submitTime as string) }}
            </EllipsisText>
          </div>
          <div class="flex items-center">
            <b>提交人：</b>
            <EllipsisText class="flex-1">
              {{ getNickNameByFieldName(item.userID, 'ID') }}
            </EllipsisText>
          </div>
        </div>
        <div class="flex flex-1">
          <b class="w-70px">提交描述：</b>
          <EllipsisText :lines="2" class="max-h-2rem flex-1 line-height-1rem">
            {{ item.description ? item.description : '暂无' }}
          </EllipsisText>
        </div>
        <div class="max-w-400px flex items-center justify-center gap-1">
          <div :class="`${prefixCls}__tag-hoverable`" :disabled="(item.labels?.length || 0) < 4">
            <div class="flex gap-1">
              <div
                v-for="labelName in item.labels"
                :key="labelName?.labelID"
                class="b-2 rounded-4 b-solid px-1 text-nowrap font-bold"
                :style="{
                  borderColor: defaultColorList[labelName.label?.color! - 1],
                  color: defaultColorList[labelName.label?.color! - 1],
                }"
              >
                {{ labelName.label?.name }}
              </div>
            </div>
          </div>
          <Icon
            icon="icon-park-outline:edit"
            class="cursor-pointer"
            @click="handleEditClLabel(item)"
          />
        </div>
      </div>
    </div>

    <template v-else-if="!isLoading">
      <div
        v-if="hasClLabelRecord"
        class="mt-4 min-w-1200px flex justify-center rounded-md bg-FO-Container-Fill1 p-6"
      >
        <div class="pl-6 font-size-16px font-bold">
          该CL号无效或未添加标签
        </div>
      </div>
      <div v-else class="mt-4 min-w-1200px flex justify-center rounded-md bg-FO-Container-Fill1 p-6">
        <div class="pl-6 font-size-16px font-bold">
          该分支暂无CL标签
        </div>
      </div>
    </template>

    <div class="mt-4 flex justify-end">
      <APagination
        v-model:current="page"
        v-model:pageSize="pageSize"
        size="small"
        :total="totalNum"
        showSizeChanger
        :pageSizeOptions="['10', '20', '50', '100']"
        :showTotal="(total) => `共 ${total} 条数据`"
        @change="handlePageChange"
      />
    </div>

    <ClLabelConfigModal @register="registerModal" @success="handleSuccess" />
    <ClRecordLabelModal
      :streamID="streamID"
      :clLabelList="clLabelList"
      @register="registerClRecordLabel"
      @success="handleSuccess"
    />
  </PageWrapper>
</template>

<script lang="ts" setup name="P4ClLabelManage">
import { useResizeObserver } from '@vueuse/core';
import { Pagination as APagination } from 'ant-design-vue';
import { onBeforeMount, onMounted, ref, unref, watch } from 'vue';
import { useRouter } from 'vue-router';
import ClLabelConfigModal from './ClLabelConfigModal.vue';
import ClRecordLabelModal from './ClRecordLabelModal.vue';
import type { ClLabelsListItem, ClRecordsListItem } from '/@/api/page/model/p4ClLabelModel';
import type { StreamsListItem } from '/@/api/page/model/p4Model';
import { getStreamsListByPage } from '/@/api/page/p4';
import { getClLabelsListByPage, getClRecordsListByPage } from '/@/api/page/p4ClLabel';
import { defaultColorList } from '/@/components/ColorPopover';
import Icon from '/@/components/Icon';
import { useModal } from '/@/components/Modal';
import { PageWrapper } from '/@/components/Page';
import { useTrack } from '/@/hooks/system/useTrack';
import { useUserList } from '/@/hooks/system/useUserList';
import { useDesign } from '/@/hooks/web/useDesign';
import { getAllPaginationList } from '/@/hooks/web/usePagination';
import { useAppStore } from '/@/store/modules/app';
import { useP4StoreWithOut } from '/@/store/modules/p4';
import { useUserStoreWithOut } from '/@/store/modules/user';
import { formatTISOToDate } from '/@/utils/dateUtil';

const { prefixCls } = useDesign('p4-cl-label-manage');
const { push, currentRoute } = useRouter();
const userStore = useUserStoreWithOut();
const p4Store = useP4StoreWithOut();
const [registerModal, { openModal }] = useModal();
const [registerClRecordLabel, { openModal: openClRecordLabelModal }] = useModal();
const { getUserList, getNickNameByFieldName } = useUserList();
const depotID = Number(unref(currentRoute).params.id);
const streamID = Number(unref(currentRoute).params.stream_id);
const hasClLabelRecord = ref<boolean>(false);
const streamName = ref<string>('');
const clNumber = ref<number>();
const streamList = ref<StreamsListItem[]>([]);
const curStream = ref<Nullable<StreamsListItem>>(null);
const clLabelList = ref<ClLabelsListItem[]>([]);
const clRecordList = ref<ClRecordsListItem[]>([]);
const isLoading = ref<boolean>(false);
const appStore = useAppStore();
const { setTrack } = useTrack();
const listHeight = ref<number>(500);
const page = ref(1);
const pageSize = ref(50);
const totalNum = ref(0);

// 清空筛选
function handleClear() {
  clNumber.value = undefined;
  setTrack('jeafzhecli');
}

// 添加CL标签
function handleCreateClLabel() {
  openClRecordLabelModal(true, {
    isUpdate: false,
  });
}

// 编辑CL标签
function handleEditClLabel(item: ClRecordsListItem) {
  openClRecordLabelModal(true, {
    isUpdate: true,
    editClRecord: item,
  });
}

// 标签配置
function handleLabelConfig() {
  openModal(true, {
    clLabelList: clLabelList.value,
  });
}

// 获取Cl标签列表
async function getClLabelList() {
  const { list } = await getAllPaginationList((p) =>
    getClLabelsListByPage(userStore.getProjectId, p),
  );
  clLabelList.value = list || [];
}

// 获取cl提交记录列表
async function getClRecordsList() {
  const { list, total } = await getClRecordsListByPage(userStore.getProjectId, {
    page: page.value,
    pageSize: pageSize.value,
    streamID,
    cl: clNumber.value ? clNumber.value : undefined,
  });
  totalNum.value = total;
  clRecordList.value = list || [];
}

// 页面左侧点击返回链接时的操作
function goBack() {
  push({ name: 'P4Depots' });
}

// 获取分支列表
async function getStreamList() {
  if (!userStore.getProjectId) {
    return;
  }
  const { list } = await getAllPaginationList((p) =>
    getStreamsListByPage(userStore.getProjectId, {
      ...p,
      depotID,
    }),
  );
  if (list?.length > 0) {
    streamList.value = list;
    curStream.value = list.find((e) => e.ID === streamID) || null;
    streamName.value = curStream.value?.description || curStream.value?.path || '';
    hasClLabelRecord.value = !!curStream.value?.hasClLabelRecord;
  } else {
    streamList.value = [];
    curStream.value = null;
    streamName.value = '';
    hasClLabelRecord.value = false;
  }
  p4Store.setCurStream(curStream.value);
}

async function handleSuccess(type?: string) {
  if (type !== 'edit') {
    page.value = 1;
  }
  await getStreamList();
  await getClLabelList();
  await getClRecordsList();
  await getUserList();
}

// 分页处理
function handlePageChange(p: number, size: number) {
  page.value = p;
  pageSize.value = size;
  getClRecordsList();
}

onBeforeMount(async () => {
  appStore.setPageLoadingAction(true);
  isLoading.value = true;
  await handleSuccess();
  appStore.setPageLoadingAction(false);
  isLoading.value = false;
});

watch(
  () => userStore.getProjectId,
  (v, oldValue) => {
    if (v && v !== oldValue) {
      // 切换项目后返回仓库列表
      push({ name: 'P4Depots' });
    }
  },
);

watch(
  () => clNumber.value,
  (val) => {
    if (val) {
      setTrack('t6ywdavxkl');
    }
    page.value = 1;
    getClRecordsList();
  },
);

onMounted(() => {
  useResizeObserver(document.body, () => {
    listHeight.value = Math.max((window.innerHeight || 600) - 236, 300);
  });
});
</script>

<style lang="less">
@prefix-cls: ~'hypergryph-p4-cl-label-manage';
.@{prefix-cls} {
  &__tag-hoverable {
    display: flex;
    position: relative;
    align-items: center;
    max-width: 400px;
    min-height: 24px;
    padding-right: 8px;
    overflow: hidden;

    &:not([disabled='true']):not(:hover) {
      &::after {
        content: '';
        position: absolute;
        z-index: 1;
        top: 0;
        right: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(to right, transparent 90%, @FO-Container-Fill1);
      }
    }

    &:not([disabled='true']):hover {
      z-index: 10;
      width: auto;
      max-width: 416px;
      overflow: visible;

      & > div {
        position: absolute;
        top: -2px;
        right: 4px;
        flex-wrap: wrap;
        width: 380px;
        padding: 4px;
        border-radius: 8px;
        background-color: @FO-Container-Fill1;
        box-shadow: 0 0 10px 0 #00000040;
        row-gap: 4px;
      }
    }
  }
}

[data-theme='dark'] {
  .@{prefix-cls} {
    &__tag-hoverable {
      &:not([disabled='true']):hover {
        & > div {
          box-shadow: 0 0 10px 0 #ffffff40;
        }
      }
    }
  }
}
</style>
