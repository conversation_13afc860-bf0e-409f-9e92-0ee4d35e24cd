import { computed, defineComponent, onActivated, onDeactivated, reactive, ref } from 'vue';
import { CuttingTaskSide } from './layout/TaskSide';
import { CuttingTask, CuttingTaskStatus, CuttingTaskType, ImageHandlingState, ImageOperation } from '@/models/cutting';
import { useCuttingHook } from './useCuttingHook';
import type { IFetcherApiResult } from '@lancercomet/fetcher';
import { useApiRequest, useNaiveUIApi } from '@/common/hooks';
import { DisplayContainer } from './layout/DisplayContainer';
import { NButton, NSwitch, NText } from 'naive-ui';
import { useNaiveModal } from '@/common/hooks/use-naive-modal.hook';
import { GlobalEnv } from '@/configs/global-env';

const CuttingPage = defineComponent({
  setup() {
    const { create } = useNaiveModal();
    const { message } = useNaiveUIApi();
    const { taskList, taskTypeList, activeImageName, currentTaskId, currentTask, allowDownload, isDrawing, onCropImage, onManualChange, onExportCurrentTask } = useCuttingHook();
    const cancelCropImageMap = reactive<Record<string, () => void>>({});
    const isShadowUse = ref(true);
    const isEdited = computed(() => {
      return taskList.value.length > 1
        || taskList.value.some((task) => task.status !== CuttingTaskStatus.PENDING || task.images.length > 0);
    });

    const cropImageHandler = async () => {
      if (!currentTask.value) {
        return;
      }
      // 获取当前任务的ID
      const currentTaskId = currentTask.value.id;
      const targetTask = taskList.value.find((task) => task.id === currentTaskId);
      try {
        const cropRequests = currentTask.value?.images.map((image) => {
          return async (): Promise<IFetcherApiResult<void>> => {
            await onCropImage(image.name, targetTask?.type, currentTaskId);
            return {
              status: 200,
            } as IFetcherApiResult<void>;
          };
        });
        const {
          mutate: mutateCropImages,
          cancel: cropImagesCancel,
        } = useApiRequest<void[], unknown[]>({
          request: cropRequests ?? [],
          concurrent: true,
          showErrorMsg: false,
          limit: 1,
        });
        cancelCropImageMap[currentTaskId] = cropImagesCancel;
        await mutateCropImages();
        if (targetTask && targetTask.status !== CuttingTaskStatus.CANCELED) {
          targetTask.status = CuttingTaskStatus.FINISHED;
          // 设置第一个图片为当前激活的图片(仅限目标任务是当前已激活的任务 且当前激活图片不是当前任务的)
          if (currentTaskId === currentTask.value.id && targetTask.safeImages?.every((image) => image.name !== activeImageName.value)) {
            activeImageName.value = targetTask.safeImages
              ?.filter((item) => item.state !== ImageHandlingState.ERROR && item.state !== ImageHandlingState.CANCELED)?.[0]
              ?.name ?? '';
          }
        }
      } catch {
        if (!targetTask) {
          return;
        }
        targetTask.images = targetTask.images.map((image) => ({
          ...image,
          state: ImageHandlingState.ERROR,
          operations: new ImageOperation(),
        }));
      }
    };

    /**
     * 处理裁切任务提交
     */
    const onHandleCrop = async (id: string) => {
      taskList.value = taskList.value.map((task) => {
        if (task.id === id) {
          task.safeImages = task.images;
          task.status = CuttingTaskStatus.PROCESSING;
          task.images.forEach((image) => {
            image.state = ImageHandlingState.PROCESSING;
          });
        }
        return task;
      });
      await cropImageHandler();
    };

    const onHandleConfirm = (imageId: string) => {
      const taskItem = taskList.value.find((task) => task.id === currentTaskId.value);
      if (!taskItem) {
        message.warning('没有找到当前任务');
        return;
      }
      const imageItem = taskItem.safeImages?.find((image) => image.name === imageId);
      if (!imageItem) {
        message.warning('没有找到当前图片');
        return;
      }
      imageItem.state = ImageHandlingState.COMPLETED;
      // 设置当前激活的图片为下一张未确认的图片
      const nextIndex = taskItem.safeImages?.findIndex((image) =>
        image.state !== ImageHandlingState.COMPLETED
        && image.state !== ImageHandlingState.CANCELED
        && image.state !== ImageHandlingState.ERROR,
      ) ?? -1;
      if (nextIndex >= 0) {
        activeImageName.value = taskItem.safeImages?.[nextIndex]?.name ?? '';
      }
    };

    const onHandleCancel = (taskId: string) => {
      if (!cancelCropImageMap[taskId]) {
        message.warning('没有正在进行的裁切任务');
        return;
      }
      cancelCropImageMap[taskId]();
      message.warning('裁切任务已取消');
      const taskItem = taskList.value.find((task) => task.id === taskId);
      if (taskItem) {
        taskItem.safeImages?.forEach((item) => {
          if (item.state === ImageHandlingState.PROCESSING) {
            item.state = ImageHandlingState.CANCELED;
          }
        });
        taskItem.status = CuttingTaskStatus.CANCELED;
      }
    };

    const onHandleExport = () => {
      if (currentTask.value?.type && [
        CuttingTaskType.ARKNIGHTS_AVATAR,
        CuttingTaskType.ARKNIGHTS_FULL_BATTLE,
        CuttingTaskType.ARKNIGHTS_FULL_DETAIL,
        CuttingTaskType.ARKNIGHTS_FULL_SKIN,
        CuttingTaskType.ARKNIGHTS_HALF,
      ].includes(currentTask.value?.type)) {
        const close = create({
          preset: 'dialog',
          title: '导出结果配置',
          content: () => (
            <div>
              <div class="flex-c-between mb-20px">
                <NText class="FO-Font-R14 block p-8px c-FO-Content-Text2">角色切图添加阴影效果</NText>
                <NSwitch v-model:value={isShadowUse.value} />
              </div>
              <div class="flex-c-end gap-8px">
                <NButton class="btn-fill-default" onClick={() => close()} secondary size="small">取消</NButton>
                <NButton
                  class="btn-fill-primary"
                  onClick={() => {
                    onExportCurrentTask(isShadowUse.value);
                    close();
                  }}
                  size="small"
                  type="primary"
                >确认并导出
                </NButton>
              </div>
            </div>
          ),
          footer: () => null,
          autoFocus: false,
        });
      } else {
        onExportCurrentTask(false);
      }
    };

    const onCreateNewTask = () => {
      taskList.value.unshift(new CuttingTask());
      currentTaskId.value = taskList.value[0].id;
      activeImageName.value = '';
    };

    const onDeleteTask = (taskId: string) => {
      taskList.value = taskList.value.filter((task) => task.id !== taskId);
      if (taskList.value.length <= 0) {
        onCreateNewTask();
      }
      currentTaskId.value = taskList.value[0].id;
      activeImageName.value = '';
    };

    const checkIsEdited = (event: BeforeUnloadEvent) => {
      // 检查是否有未保存的编辑内容
      if (isEdited.value && !allowDownload.value) {
        // 在事件中设置提示消息
        event.returnValue = '您有未保存的内容，确定要离开吗？';
      }
    };

    onActivated(() => {
      if (!GlobalEnv.isDevelopment) {
        window.addEventListener('beforeunload', checkIsEdited);
      }
    });
    onDeactivated(() => {
      window.removeEventListener('beforeunload', checkIsEdited);
    });

    return () => (
      <div class="cutting-page h-[calc(100vh-64px)] p-24px">
        <div class="cutting-page-container h-full flex gap-16px">
          <CuttingTaskSide
            activeImageKey={activeImageName.value}
            currentKey={currentTaskId.value}
            isDrawing={isDrawing.value}
            list={taskList.value}
            onCancel={onHandleCancel}
            onCreateNewTask={onCreateNewTask}
            onRemoveTaskItem={onDeleteTask}
            onSubmit={onHandleCrop}
            onUpdateActiveImageKey={(taskId, image) => {
              const targetTask = taskList.value.find((task) => task.id === taskId);
              if (!targetTask) {
                return;
              }
              if (![ImageHandlingState.CANCELED, ImageHandlingState.ERROR, ImageHandlingState.PROCESSING, ImageHandlingState.SLICING].includes(image.state)) {
                activeImageName.value = image.name;
              }
            }}
            onUpdateImages={(index, images) => {
              const targetTask = taskList.value[index];
              if (targetTask) {
                targetTask.images = images;
              }
            }}
            onUpdateKey={(id) => {
              currentTaskId.value = id;
              const targetTask = taskList.value.find((task) => task.id === id);
              if (targetTask && targetTask.safeImages?.length) {
                if (targetTask.status === CuttingTaskStatus.PROCESSING) {
                  activeImageName.value = '';
                  return;
                }
                activeImageName.value = targetTask.safeImages
                  ?.filter((item) => item.state !== ImageHandlingState.ERROR && item.state !== ImageHandlingState.CANCELED)?.[0]
                  ?.name ?? '';
              } else {
                activeImageName.value = '';
              }
            }}
            onUpdateTaskType={(type) => {
              const targetTask = taskList.value.find((task) => task.id === currentTaskId.value);
              if (targetTask) {
                targetTask.type = type;
              }
            }}
            taskTypeList={taskTypeList.value}
          />
          <DisplayContainer
            activeImageName={activeImageName.value}
            images={currentTask.value?.safeImages ?? []}
            isDrawing={isDrawing.value}
            onDrawingChange={(value) => {
              isDrawing.value = value;
            }}
            onExport={onHandleExport}
            onManualChange={onManualChange}
            onSubmit={onHandleConfirm}
            taskId={currentTaskId.value}
            type={currentTask.value?.type}
          />
        </div>
      </div>
    );
  },
});

export {
  CuttingPage,
};
