<template>
  <div class="h-full flex flex-col overflow-hidden">
    <div class="flex items-center gap-2">
      <AButton type="primary" :disabled="websocketStore.loading" @click="() => quickCap()">
        截图
      </AButton>
      <AButton :disabled="!websocketStore.screenshotList.length" @click="() => removeScreen()">
        清空
      </AButton>
    </div>
    <div class="screenshot-content h-full flex-1 overflow-hidden py-4">
      <div v-if="websocketStore.screenshotList.length === 0" class="py-8 c-FO-Content-Text2">
        <AEmpty description="暂无截图" />
      </div>
      <div v-else class="h-full flex flex-1 flex-wrap justify-start gap-4 overflow-y-auto">
        <div
          v-for="(screenshot, index) in websocketStore.screenshotList" :key="index"
          class="h-fit w-40 flex flex-col items-center overflow-hidden b-1 b-FO-Container-Stroke2 rounded-lg p-2"
        >
          <img :src="screenshot.url" class="h-auto w-full">
          <div class="p-2 text-center">
            <div class="mb-2 text-sm c-FO-Content-Text2">
              {{ screenshot.time.format('HH:mm:ss') }}
            </div>
            <AButton type="primary" @click="() => downloadImg(screenshot)">
              <Icon :icon="Download" :size="16" class="mr-1" />
              保存图片
            </AButton>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import Download from '@iconify-icons/icon-park-outline/download';
import { Button as AButton, Empty as AEmpty } from 'ant-design-vue';
import { Icon } from '/@/components/Icon';
import { useScreenshot } from '../../composables/useScreenshot';
import { useCloudDeviceWebsocketStore } from '../../stores';

const websocketStore = useCloudDeviceWebsocketStore();
const { quickCap, removeScreen, downloadImg } = useScreenshot();
</script>
