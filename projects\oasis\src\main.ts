import AppComponent from './App.vue';
import { createApp } from 'vue';
import { initAppConfigStore } from '/@/logics/initAppConfig';
import { setupRouter } from './router';
import { setupStore, store } from '/@/store';
import { setupGlobDirectives } from '/@/directives';
import { registerGlobComp } from '/@/components/registerGlobComp';
import microApp from '@micro-zoe/micro-app';
import { LoadSubSysStatus, useLoadSubSys } from './store/modules/loadSubSys.ts';

import '@hg-tech/forgeon-style/style';
// 此处common样式必须早于unocss引入，否则会导致样式覆盖unocss并产生优先级问题，如媒体查询失效等问题
import '@hg-tech/oasis-common/style';
import 'virtual:uno.css';
import 'virtual:svg-icons-register';
import './styles/index.less';

import { initSentry } from './vendors/errorTrace.ts';

async function bootstrap() {
  const app = createApp(AppComponent);

  initSentry(app);
  setupStore(app);
  initAppConfigStore();
  registerGlobComp(app);
  setupGlobDirectives(app);
  const router = setupRouter(app);

  const { setStatus } = useLoadSubSys(store);
  microApp.router.setBaseAppRouter(router);
  microApp.start({
    aHrefResolver(hrefValue) {
      /**
       * 这里除了http其实还会有其他协议的链接，比如blob:和data:或者其他协议头
       * @description 如果hrefValue是相对路径，则将其转换为主应用的绝对路径
       */
      return new URL(hrefValue, window.location.origin).href;
    },
    lifeCycles: {
      created(_, appName) {
        setStatus(appName, LoadSubSysStatus.Loading);
      },
      aftershow(_, appName) {
        setStatus(appName, LoadSubSysStatus.Loaded);
      },
      mounted(_, appName) {
        setStatus(appName, LoadSubSysStatus.Loaded);
      },
      error(_, appName) {
        setStatus(appName, LoadSubSysStatus.Error);
      },
      unmount(_, appName) {
        setStatus(appName, undefined);
      },
    },
  });

  app.mount('#app');
}

bootstrap();
