<template>
  <Spin :spinning="loading">
    <div :class="prefixCls" class="rounded-md bg-FO-Container-Fill1 p-4">
      <template v-if=" swarmGroupInfo">
        <div class="FO-Font-B18 flex justify-between">
          <div v-if="!isEditName" :class="`${prefixCls}__name`" @click="changeToEditName">
            <span>{{ curReviewGroup?.name }}</span>
            <Icon icon="bx:edit-alt" class="ml-1" />
          </div>
          <template v-else>
            <div>
              <Input
                ref="groupNameRef"
                v-model:value="groupName"
                placeholder="请输入名称"
                class="!w-[200px]"
                :maxlength="20"
              />
              <Button
                type="text"
                size="small"
                title="撤销"
                class="ml-2"
                @click="handleEditNameRevert"
              >
                <Icon icon="icon-park-outline:return" />
              </Button>
              <Button type="text" size="small" title="保存" @click="handleEditName">
                <Icon icon="charm:tick" class="!c-FO-Functional-Success1-Default" />
              </Button>
            </div>
          </template>
        </div>
        <div class="mt-6">
          <BorderBox class="edit-area">
            <template #title="{ titleClass }">
              <div :class="[titleClass]">
                <span class="mr-[6px]">
                  审批配置
                </span>
                <Popconfirm :title="`确认${isLock ? '关闭' : '开启'}审批配置？`" @confirm="() => handleApproveChange(!isLock)">
                  <Switch
                    :checked="isLock"
                    checkedChildren="开"
                    unCheckedChildren="关"
                    size="small"
                  />
                </Popconfirm>
              </div>
            </template>
            <template #subTitle="{ subTitleClass }">
              <div class="flex justify-between" :class="subTitleClass">
                <div>若开启审批，提交中心会等待审批通过后执行提交（若开启审查，则需审查也通过后才能执行提交）。</div>
                <a class="ml-[4px] px-[4px] c-FO-Content-Text1" @click="handleEditApproveConfig">
                  <Icon class="edit-icon" icon="bx:edit-alt" />
                </a>
              </div>
            </template>
            <div class="FO-Font-B14">
              审批人：
            </div>
            <div class="my-[8px] flex items-center gap-[8px]">
              <template v-if="curReviewGroup?.lockGroup?.isChat">
                从单号获取审批QA，无法获取时通知群：{{ curReviewGroup.lockGroup?.chatName || '未配置' }}
              </template>
              <template v-else-if="curReviewGroup?.lockGroup?.qaIds?.length">
                <span v-for="i in curReviewGroup?.lockGroup?.qaIds" :key="i">
                  {{ formatNickName(userList?.find(item => item.ID === i)) }}
                </span>
              </template>
              <template v-else>
                暂无
              </template>
            </div>
            <div v-if="approveTimeoutNotification" class="FO-Font-B14">
              审批超时通知：
            </div>
            <div class="my-[8px]">
              {{ approveTimeoutNotification }}
            </div>
          </BorderBox>
          <BorderBox class="edit-area">
            <template #title="{ titleClass }">
              <div :class="[titleClass]">
                <span class="mr-[6px]">
                  review配置
                </span>
                <Popconfirm :title="`确认${curReviewGroup?.isValid ? '关闭' : '开启'}review配置？`" @confirm="() => handleAuditChange(!curReviewGroup?.isValid)">
                  <Switch
                    :checked="curReviewGroup?.isValid"
                    checkedChildren="开"
                    unCheckedChildren="关"
                    size="small"
                  />
                </Popconfirm>
              </div>
            </template>
            <template #subTitle="{ subTitleClass }">
              <div class="flex justify-between" :class="subTitleClass">
                <div>
                  若开启review，提交时会自动创建Swarm，审查人会收到审查通知；若未开启仅reviewer可通过，项目成员都有权使swarm通过。
                </div>
                <a class="ml-[4px] px-[4px] c-FO-Content-Text1" @click="handleEditAuditConfig">
                  <Icon class="edit-icon" icon="bx:edit-alt" />
                </a>
              </div>
            </template>
            <div class="mt-[12px] flex flex-col gap-[8px]">
              <div class="FO-Font-B14">
                reviewer：
              </div>
              <UserDisplayRow
                :users="curReviewGroup?.defaultReviewers?.map(i => i.user)"
                :userGroupNames="curReviewGroup?.defaultReviewerGroups"
                :serverID="serverID"
              />
              <div v-if="isReviewersCheck" class="flex items-center">
                <Peoples class="mr-[4px] flex" />
                <span>将所有reviewer作为Swarm跟进人</span>
              </div>
              <div v-if="isModeratorsCheck" class="flex items-center">
                <RightUser class="mr-[4px] flex" />
                <span>仅reviewer可通过Swarm</span>
              </div>
            </div>
          </BorderBox>
          <BorderBox class="edit-area">
            <template #title="{ titleClass }">
              <div :class="[titleClass]">
                <span class="mr-[6px]">
                  关注配置
                </span>
                <Popconfirm :title="`确认${swarmGroupInfo.concernGroup?.isValid ? '关闭' : '开启'}关注配置？`" @confirm="() => handleConcernChange(!swarmGroupInfo.concernGroup?.isValid)">
                  <Switch
                    :checked="swarmGroupInfo.concernGroup?.isValid"
                    checkedChildren="开"
                    unCheckedChildren="关"
                    size="small"
                  />
                </Popconfirm>
              </div>
            </template>
            <template #subTitle="{ subTitleClass }">
              <div class="flex justify-between" :class="subTitleClass">
                <div>
                  若开启关注，配置了上方的reviewer后，被关注人提交时会自动创建Swarm，reviewer会收到审查通知；关注配置与上方review配置共用review超时通知配置。
                </div>
                <a class="ml-[4px] px-[4px] c-FO-Content-Text1" @click="handleEditConcernConfig">
                  <Icon class="edit-icon" icon="bx:edit-alt" />
                </a>
              </div>
            </template>
            <div class="mt-[12px] flex flex-col gap-[8px]">
              <div class="FO-Font-B14">
                被关注人：
              </div>
              <UserDisplayRow
                :users="swarmGroupInfo.concernGroup?.manualUsers"
                :userGroupNames="swarmGroupInfo.concernGroup?.p4Groups"
                :serverID="serverID"
              />
              <div v-if="swarmGroupInfo.concernGroup?.mustReviewConcernSubmitOption" class="flex items-center">
                <MindmapMap class="mr-[4px] flex" />
                <span>被关注人提交任何路径都触发该review配置</span>
              </div>
            </div>
          </BorderBox>
          <BorderBox label="路径配置">
            <template #subTitle="{ subTitleClass }">
              <div :class="subTitleClass">
                若干员的提交包含所填写或所勾选路径，则会触发开启状态的审批、Review或关注组，提交工具会强制要求干员的提交通过这些流程。
              </div>
            </template>
            <div class="edit-area mb-[20px]">
              <div class="flex items-center justify-between">
                <div class="FO-Font-B14">
                  填写路径：
                </div>
                <a class="ml-[4px] px-[4px] c-FO-Content-Text1" @click="handleEditPathRegexConfig">
                  <Icon class="edit-icon" icon="bx:edit-alt" />
                </a>
              </div>
              <div v-if="swarmGroupInfo?.reviewGroup?.customizeReviewPaths?.length" class="flex flex-wrap gap-[24px]">
                <span v-for="i in swarmGroupInfo.reviewGroup.customizeReviewPaths " :key="i.regex">{{ i.regex }}</span>
              </div>
              <div v-else>
                暂无
              </div>
            </div>
            <Divider class="mb-[16px] mt-[12px]" />
            <div class="edit-area">
              <div class="flex items-center justify-between">
                <div class="FO-Font-B14">
                  勾选路径：
                </div>
                <a class="ml-[4px] px-[4px] c-FO-Content-Text1" @click="handleEditPathConfig">
                  <Icon class="edit-icon" icon="bx:edit-alt" />
                </a>
              </div>
              <P4AuditTree
                :key="JSON.stringify(swarmGroupInfo?.reviewGroup?.reviewPaths)"
                ref="p4TreeRef"
                disabled
                :class="`${prefixCls}__tree`"
                :permissionList="swarmGroupInfo.reviewGroup?.reviewPaths"
              />
            </div>
          </BorderBox>
        </div>
      </template>
      <Empty v-else class="mt-2" />
    </div>
    <ConfigApproveModal @register="registerConfigApproveModal" @success="() => emit('success')" />
    <ConfigAuditModal @register="registerConfigAuditModal" @success="() => emit('success')" />
    <ConfigPathModal @register="registerConfigPathModal" @success="() => emit('success')" />
    <ConfigPathRegexModalHolder />
    <ConfigConcernModalHolder />
  </Spin>
</template>

<script lang="ts" setup>
import type { SwarmGroupItemInfo, SwarmReviewGroupsConcernParams } from '../../../api/page/model/swarmModel';
import type { FeishuChatListItem } from '../../../api/page/model/systemModel';
import { MindmapMap, Peoples, RightUser } from '@icon-park/vue-next';
import { type TreeSelectProps, Button, Divider, Empty, Input, Popconfirm, Spin, Switch } from 'ant-design-vue';
import { type PropType, computed, nextTick, ref, watch, watchEffect } from 'vue';
import { useRouter } from 'vue-router';
import { editAuditPath, editSwarmGroupName, updateSwarmReviewGroupsConcern } from '../../../api/page/swarm.ts';
import { BorderBox } from '../../../components/Form/index.ts';
import { Icon } from '../../../components/Icon/index.ts';
import { useTrack } from '../../../hooks/system/useTrack.ts';
import { useDesign } from '../../../hooks/web/useDesign.ts';
import { useMessage } from '../../../hooks/web/useMessage.tsx';
import { useUserStoreWithOut } from '../../../store/modules/user.ts';
import P4AuditTree from './components/p4AuditTree.vue';
import { formatNickName } from '../../../hooks/system/useUserList.ts';
import { useModal } from '../../../components/Modal/index.ts';
import ConfigApproveModal from './components/ConfigApproveModal.vue';
import ConfigAuditModal from './components/ConfigAuditModal.vue';
import ConfigPathModal from './components/ConfigPathModal.vue';
import ConfigPathRegexModal from './components/ConfigPathRegexModal.vue';
import ConfigConcernModal from './components/ConcernModal.vue';
import { formatTimeDeclare } from './TimeInput/utils.ts';
import { useTimeoutConfig } from './useTimeoutConfig.ts';
import UserDisplayRow from '../../../components/UserDisplayRow.vue';
import { useSysUserList } from '../../../hooks/useUserList.ts';
import { useModalShow } from '@hg-tech/utils-vue';
import type { RegexCustomPath } from '/@/api/page/model/p4Model.ts';

const props = defineProps({
  swarmGroupInfo: {
    type: Object as PropType<SwarmGroupItemInfo>,
  },
  groupList: {
    type: Array as PropType<TreeSelectProps['treeData']>,
    default: () => [],
  },
  loading: {
    type: Boolean,
    default: false,
  },
  chatList: {
    type: Array as () => FeishuChatListItem[],
    default: () => [],
  },
  reviewProjectID: {
    type: Number,
  },
  allSwarmGroupList: {
    type: Array as PropType<SwarmGroupItemInfo[]>,
    default: () => [],
  },
});

const emit = defineEmits<{
  (event: 'editOrRevert', isEdit: boolean,): void;
  (event: 'success',): void;
  (event: 'switchApprove', needLock: boolean): void;
  (event: 'switchAudit', needLock: boolean): void;
}>();

const { prefixCls } = useDesign('swarm-preview-page');
const { currentRoute } = useRouter();

const swarmGroupId = computed(() => props.swarmGroupInfo?.swarmReviewGroup?.ID);
const curReviewGroup = computed(() => props.swarmGroupInfo?.reviewGroup);
const serverID = computed(() => Number(currentRoute.value?.query?.sID));
const streamID = computed(() => Number(currentRoute.value?.params?.stream_id));

const { createMessage } = useMessage();
const userStore = useUserStoreWithOut();
const groupName = ref('');
const isLock = ref(false);
const p4TreeRef = ref();
const isEditName = ref(false);
const groupNameRef = ref();
const isModeratorsCheck = ref(false);
const isReviewersCheck = ref(false);
const { setTrack } = useTrack();
const [registerConfigApproveModal, { openModal: openConfigApproveModal }] = useModal();
const [registerConfigAuditModal, { openModal: openConfigAuditModal }] = useModal();
const [registerConfigPathModal, { openModal: openConfigPathModal }] = useModal();

const [ConfigPathRegexModalHolder, showConfigPathRegexModal] = useModalShow(ConfigPathRegexModal);
const [ConfigConcernModalHolder, openConfigConcernModal] = useModalShow(ConfigConcernModal);
const { userList } = useSysUserList();

const { notifyGroupOptions: checkNotifyGroups, permissionProcessOptions } = useTimeoutConfig('check');
const { notifyGroupOptions: reviewNotifyGroups } = useTimeoutConfig('review');

const approveTimeoutNotification = computed(() => {
  const timeoutConfig = curReviewGroup.value?.lockGroup?.timeoutConfig;

  const notifier = [
    ...(timeoutConfig?.additionalNotifier?.specialGroups || []),
    ...(timeoutConfig?.additionalNotifier?.members || []),
  ];

  if (!timeoutConfig?.timeLimit || (!timeoutConfig.additionalNotifyChatGroupIDs?.length && !notifier.length)) {
    // 配置无效
    return undefined;
  }

  const timeoutStr = `若审批通知发出 ${formatTimeDeclare(timeoutConfig.timeLimit)} 后未处理，`;

  const groupNames = (timeoutConfig.additionalNotifyChatGroupIDs || []).map((id) => {
    const group = props.chatList?.find((e) => e.chat_id === id);
    return group?.name;
  });
  const notifierGroup = notifier.map((id) => {
    const user = checkNotifyGroups.value.find((e) => e.value === id);
    return user?.label;
  });
  const allNotify = [...notifierGroup, ...groupNames];
  const notifierStr = `则向${allNotify.join('、')}发送超时提示`;

  const permissionProcess = [
    ...(timeoutConfig?.permissionProcess?.specialGroups || []),
    ...(timeoutConfig?.permissionProcess?.members || []),
  ].map((id) => {
    const user = permissionProcessOptions.value.find((e) => e.value === id);
    return user?.label;
  });
  const permissionStr = permissionProcess.length ? `，仅${permissionProcess.join('、')}可以点击提示通知完成审批。` : '。';

  return timeoutStr + notifierStr + permissionStr;
});
const auditTimeoutNotification = computed(() => {
  const timeoutConfig = curReviewGroup.value?.timeoutConfig;

  const notifier = [
    ...(timeoutConfig?.additionalNotifier?.specialGroups || []),
    ...(timeoutConfig?.additionalNotifier?.members || []),
  ];

  if (!timeoutConfig?.timeLimit || (!timeoutConfig.additionalNotifyChatGroupIDs?.length && !notifier.length)) {
    // 配置无效
    return undefined;
  }

  const timeoutStr = `若审查通知发出 ${formatTimeDeclare(timeoutConfig.timeLimit)} 后未处理，`;

  const groupNames = (timeoutConfig.additionalNotifyChatGroupIDs || []).map((id) => {
    const group = props.chatList?.find((e) => e.chat_id === id);
    return group?.name;
  });
  const notifierGroup = notifier.map((id) => {
    const user = reviewNotifyGroups.value.find((e) => e.value === id);
    return user?.label;
  });
  const allNotify = [...notifierGroup, ...groupNames];
  const notifierStr = `则向${allNotify.join('、')}发送超时提示。`;

  return timeoutStr + notifierStr;
});

watchEffect(() => {
  groupName.value = curReviewGroup.value?.name || '';
  isLock.value = curReviewGroup.value?.lockStatus === 3;
  isModeratorsCheck.value = !!curReviewGroup.value?.isModerators;
  isReviewersCheck.value = !!curReviewGroup.value?.isReviewers;
});

watch(() => props.swarmGroupInfo, () => {
  isEditName.value = false;
});

async function handleApproveChange(val: boolean) {
  emit('switchApprove', val);
}
function handleConcernChange(val: boolean) {
  emit('switchConcern', val);
}
async function handleAuditChange(val: boolean) {
  emit('switchAudit', val);
}

function changeToEditName() {
  emit('editOrRevert', true);
  setTrack('ixumuexybr');
  isEditName.value = true;
  nextTick(() => {
    groupNameRef.value.focus();
  });
}

function handleEditNameRevert() {
  isEditName.value = false;
  groupName.value = curReviewGroup.value?.name || '';
  emit('editOrRevert', false);
}

// 判断name是否在group中重复
function isNameDuplicate(name?: string) {
  const findName = props.allSwarmGroupList.find((e) => {
    return e.reviewGroup?.ID !== curReviewGroup.value?.ID && e.reviewGroup?.name?.toLowerCase() === name?.toLowerCase();
  });

  if (findName) {
    createMessage.warning('审查组名称重复(不区分大小写)');
  }

  return findName;
}

async function handleEditName() {
  if (isNameDuplicate(groupName.value) || !swarmGroupId.value) {
    return;
  }

  await editSwarmGroupName(
    userStore.getProjectId,
    {
      name: groupName.value,
      swarmReviewGroupID: swarmGroupId.value,
      streamID: streamID.value,
    },
  );

  createMessage.success('更新成功');

  setTrack('ydnsgqmpjo');
  emit('success');
  isEditName.value = false;
}

async function handleEditApproveConfig() {
  openConfigApproveModal(true, {
    chatList: props.chatList,
    swarmGroup: props.swarmGroupInfo,
    streamID: streamID.value,
  });
}

async function handleEditAuditConfig() {
  openConfigAuditModal(true, {
    reviewProjectID: props.reviewProjectID,
    swarmGroup: props.swarmGroupInfo,
    chatList: props.chatList,
    groupList: props.groupList,
    streamID: streamID.value,
  });
}

async function handleEditConcernConfig() {
  await openConfigConcernModal({
    concernGroup: props.swarmGroupInfo?.concernGroup,
    groupList: props.groupList,
    async sentReq(formValue: SwarmReviewGroupsConcernParams) {
      const res = await updateSwarmReviewGroupsConcern(userStore.getProjectId!, { ...formValue, streamID: streamID.value, swarmReviewGroupID: swarmGroupId.value,
      });
      return res;
    },
  });
  emit('success');
}

async function handleEditPathRegexConfig() {
  if (!props.swarmGroupInfo?.reviewGroup?.ID) {
    return;
  }
  await showConfigPathRegexModal({
    regexList: props.swarmGroupInfo?.reviewGroup?.customizeReviewPaths?.map((item: RegexCustomPath) => ({ ...item })) || [],
    async sentReq(regexList: RegexCustomPath[]) {
      const res = editAuditPath(userStore.getProjectId!, {
        customizePaths: regexList,
        paths: props.swarmGroupInfo?.reviewGroup?.reviewPaths,
        groupID: props.swarmGroupInfo?.reviewGroup?.ID,
        streamID: streamID.value!,
        swarmReviewGroupID: props.swarmGroupInfo?.swarmReviewGroup?.ID,
      }).then((res) => res?.data?.data);
      return res;
    },
  });
  emit('success');
}
async function handleEditPathConfig() {
  openConfigPathModal(true, {
    reviewProjectID: props.reviewProjectID,
    swarmGroup: props.swarmGroupInfo,
    groupList: props.groupList,
    streamID: streamID.value,
  });
}
</script>

<style lang="less">
@prefix-cls: ~'hypergryph-swarm-preview-page';
.@{prefix-cls} {
  position: relative;

  &__name {
    width: fit-content;
    height: 32px;
    cursor: pointer;
  }

  &__card {
    position: relative;
    background-color: @member-card-background;
    padding: 6px 12px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    flex-wrap: wrap;

    &-content {
      margin-bottom: 0 !important;
      width: 92%;

      &[is-large='true'] {
        margin-top: 8px !important;
        margin-bottom: 8px !important;
      }
    }

    &-ellipsis-btn {
      position: absolute;
      right: 5px;
      top: 13px;
    }

    &-icon {
      margin-right: 8px;
      padding: 3px;
      border-radius: 4px;
      height: fit-content;
      background-color: @FO-Brand-Primary-Default;
      color: @FO-Content-Components1;
      cursor: pointer;

      &[disabled='true'] {
        background-color: @FO-Container-Fill5;
        cursor: not-allowed;
      }
    }
  }

  &__tree {
    margin: 10px 20px 0 10px;
  }

  & .ant-tree .ant-tree-treenode-disabled .ant-tree-node-content-wrapper {
    color: @FO-Content-Text1;
  }

  .edit-icon {
    cursor: pointer;
    transition: 300ms;
  }
  .edit-area:hover .edit-icon {
    color: @FO-Brand-Primary-Default;
  }
}
</style>
