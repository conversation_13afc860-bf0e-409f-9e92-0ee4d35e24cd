<template>
  <APopover trigger="click" placement="bottom" @openChange="openChange">
    <template #content>
      <div class="w-200px">
        <div class="flex justify-between">
          <ACheckbox
            v-model:checked="checkAll"
            :indeterminate="indeterminate"
            @change="onCheckAllChange"
          >
            全部
          </ACheckbox>
          <span class="cursor-pointer c-FO-Brand-Primary-Default" @click="reset">重置</span>
        </div>
        <ACheckboxGroup v-model:value="checkedList">
          <ARow class="checkbox-group-row">
            <ACol v-for="(item, index) in plainOptions" :key="String(item?.dataIndex) || index" :span="24" class="checkbox-col relative" :class="{ static: ['deviceName', 'fsmState', 'action'].includes(String(item?.dataIndex) ?? '') }">
              <ACheckbox :value="item.dataIndex" :disabled="['deviceName', 'fsmState', 'action'].includes(String(item?.dataIndex) ?? '')">
                {{ item.title }}
              </ACheckbox>
              <div v-if="!['deviceName', 'fsmState', 'action'].includes(String(item?.dataIndex) ?? '')" :class="{ 'cursor-grab': !['deviceName', 'fsmState', 'action'].includes(String(item?.dataIndex) ?? '') }" class="i-ic:round-drag-handle drag-btn absolute left-85% top-0 mr-4 hidden h-20px w-20px" />
            </ACol>
          </ARow>
        </ACheckboxGroup>
      </div>
    </template>
    <BasicButton>
      <div class="flex items-center gap-4px">
        <Icon :icon="SettingTwoIcon" :size="16" />
        <span> 显示字段</span>
      </div>
    </BasicButton>
  </APopover>
</template>

<script lang="ts" setup>
import { Checkbox as ACheckbox, CheckboxGroup as ACheckboxGroup, Col as ACol, Popover as APopover, Row as ARow } from 'ant-design-vue';
import { Icon } from '/@/components/Icon';
import { BasicButton } from '/@/components/Button';
import { nextTick, onMounted, ref, watch } from 'vue';
import { usetableView } from './tableView.hook';
import { getAssetDevicesViewApi, setAssetDevicesViewApi } from '/@/api/page/deptAsset';
import type { BasicColumn } from '/@/components/Table';
import { useSortable } from '/@/hooks/web/useSortable';
import type { DevicesViewModel } from '/@/api/page/model/deptAssetModel';
import { cloneDeep } from 'lodash';
import { isNullOrUnDef } from '/@/utils/is';
import SettingTwoIcon from '@iconify-icons/icon-park-outline/setting-two';

const props = withDefaults(defineProps<{
  nowTab?: 'allDevice' | 'myDevice' | 'deviceManage' | 'myOccupy';
  viewSetting: DevicesViewModel;
}>(), {
  nowTab: 'allDevice',
  viewSetting: () => ({}),
});
const emits = defineEmits<{
  changeColumn: [BasicColumn[]];

}>();
const { getColumns } = usetableView();
const plainOptions = ref<BasicColumn[]>([]);
const checkAll = ref(false);
const indeterminate = ref(false);
let isReset = false;
const checkedList = ref<string[]>([]);
const isFri = ref(true);
const assetDevicesView = ref<DevicesViewModel>({});
function onCheckAllChange(e: any) {
  if (e.target.checked) {
    checkedList.value = plainOptions.value
      .map((item) => item.dataIndex)
      .filter((dataIndex): dataIndex is string => dataIndex !== undefined);
  } else {
    checkedList.value = [
      'deviceName',
      'fsmState',
      'action',
    ];
  }
  indeterminate.value = false;
}
async function reset() {
  isReset = true;
  checkedList.value = [];
  getColumns(props.nowTab)?.forEach((item) => {
    if (item.ifShow && item.dataIndex) {
      checkedList.value.push(String(item?.dataIndex));
    }
  });
  plainOptions.value = getColumns(props.nowTab);
  const { view } = await getAssetDevicesViewApi();
  if (!view) {
    return;
  }
  assetDevicesView.value = JSON.parse(view || '{}');
  assetDevicesView.value[props.nowTab] = plainOptions.value;
  await setAssetDevicesViewApi({ view: JSON.stringify(assetDevicesView.value) });
  emits('changeColumn', plainOptions.value);
  isReset = false;
}
// 初始化拖拽
function initDrag() {
  nextTick(() => {
    const el = document.querySelector('.checkbox-group-row') as HTMLElement;
    const { initSortable } = useSortable(el, {
      handle: `.drag-btn`,
      filter: '.static',
      onMove(evt) {
        return !evt.related.classList.contains('static');
      },
      onEnd: async ({ oldIndex, newIndex }) => {
        if (isNullOrUnDef(oldIndex) || isNullOrUnDef(newIndex) || oldIndex === newIndex) {
          return;
        }
        const clonePlainOptions = cloneDeep(plainOptions.value);
        const currentGroup = clonePlainOptions[oldIndex];
        clonePlainOptions.splice(oldIndex, 1);
        clonePlainOptions.splice(newIndex, 0, currentGroup);
        const { view } = await getAssetDevicesViewApi();
        assetDevicesView.value = JSON.parse(view);
        assetDevicesView.value[props.nowTab] = clonePlainOptions;
        plainOptions.value = cloneDeep(clonePlainOptions);
        await setAssetDevicesViewApi({ view: JSON.stringify(assetDevicesView.value) });
        emits('changeColumn', clonePlainOptions);
      },
    });
    initSortable();
  });
}
function openChange(visible: boolean) {
  if (visible) {
    initDrag();
  }
}
onMounted(async () => {
  const viewSetting: DevicesViewModel = props.viewSetting;
  assetDevicesView.value = viewSetting || {};
  assetDevicesView.value?.[props.nowTab]?.forEach((item) => {
    item.fixed = getColumns(props.nowTab).find((defaultItem) => defaultItem.dataIndex === item.dataIndex)?.fixed || false;
    item.width = getColumns(props.nowTab).find((defaultItem) => defaultItem.dataIndex === item.dataIndex)?.width || 0;
    item.title = getColumns(props.nowTab).find((defaultItem) => defaultItem.dataIndex === item.dataIndex)?.title || '';
  });
  getColumns(props.nowTab).forEach((item) => {
    if (!viewSetting?.[props.nowTab]?.find((viewSettingItem) => viewSettingItem.dataIndex === item.dataIndex)) {
      if (item.dataIndex === 'deviceUDID') {
        assetDevicesView.value?.[props.nowTab]?.splice((viewSetting[props.nowTab]?.findIndex((item) => item.dataIndex === 'assetNo') || 0) + 1, 0, item);
      } else {
        assetDevicesView.value?.[props.nowTab]?.splice((viewSetting[props.nowTab]?.length || 0) - 2, 0, item);
      }
    }
  });
  if (assetDevicesView.value?.[props.nowTab]) {
    plainOptions.value = assetDevicesView.value?.[props.nowTab] || [];
  } else {
    plainOptions.value = getColumns(props.nowTab);
  }
  const defaultShowValue: string[] = plainOptions.value.filter((item) => item.ifShow).map((item) => item.dataIndex) as string[];
  checkedList.value = defaultShowValue;
});

watch(
  () => checkedList.value,
  async (val) => {
    indeterminate.value = !!val.length && val.length < plainOptions.value.length;
    checkAll.value = val.length === plainOptions.value.length;

    if (!isFri.value && !isReset) {
      const clonePlainOptions = cloneDeep(plainOptions.value);
      clonePlainOptions.forEach((item) => {
        if (checkedList.value.includes(item.dataIndex as string)) {
          item.ifShow = true;
        } else {
          item.ifShow = false;
        }
        item.width = getColumns(props.nowTab).find((defaultItem) => defaultItem.dataIndex === item.dataIndex)?.width;
      });
      const { view } = await getAssetDevicesViewApi();
      assetDevicesView.value = JSON.parse(view || '{}');
      assetDevicesView.value[props.nowTab] = clonePlainOptions;
      plainOptions.value = cloneDeep(clonePlainOptions);
      await setAssetDevicesViewApi({ view: JSON.stringify(assetDevicesView.value) });
      emits('changeColumn', clonePlainOptions);
    }
    isFri.value = false;
  },
);
</script>

<style lang='less'>
.checkbox-col:hover {
  .drag-btn {
    display: block;
  }
}
</style>
