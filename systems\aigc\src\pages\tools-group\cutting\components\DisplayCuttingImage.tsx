import { createOfflineCanvas } from '@/common/components/editor/utils';
import { Loading3QuartersOutlined } from '@/common/components/svg-icons';
import type { ImageOperation } from '@/models/cutting';
import { NIcon, NText } from 'naive-ui';
import { type PropType, defineComponent, onMounted, ref, watch } from 'vue';

const DisplayCuttingImage = defineComponent({
  props: {
    src: {
      type: String as PropType<string>,
      required: true,
    },
    operation: {
      type: Object as PropType<ImageOperation>,
      default: () => {},
    },
  },
  setup(props) {
    const canvasRef = ref<HTMLCanvasElement | null>(null);
    const displayWidth = ref(0);
    const displayHeight = ref(0);
    const loading = ref(false);

    // 计算canvas应有的显示比例和样式，使其自适应外层div且保持裁切比例
    function getCanvasStyle(displayWidth: number, displayHeight: number) {
      return {
        display: 'block',
        margin: 'auto',
        maxWidth: '100%',
        maxHeight: '100%',
        aspectRatio: `${displayWidth} / ${displayHeight}`,
        width: 'auto',
        height: 'auto',
      };
    }

    // 渲染图片到canvas，canvas尺寸与裁切尺寸一致，不压缩原图比例
    function renderImageToCanvas(img: HTMLImageElement): Promise<void> {
      return new Promise((resolve, reject) => {
        try {
          const {
            anchorX: x = 0,
            anchorY: y = 0,
            width: cropWidth,
            height: cropHeight,
            rotate = 0,
            scale,
          } = props.operation || {};

          const canvas = canvasRef.value;
          if (!canvas) {
            resolve();
            return;
          }
          const width = cropWidth || img.width;
          const height = cropHeight || img.height;

          canvas.width = width || img.width;
          canvas.height = height || img.height;

          const ctx = canvas.getContext('2d');
          if (!ctx) {
            resolve();
            return;
          }

          ctx.drawImage(
            createOfflineCanvas(img, {
              anchorX: x,
              anchorY: y,
              width: cropWidth,
              height: cropHeight,
              rotate,
              scale,
            }),
            0,
            0,
          );
          ctx.restore();
          resolve();
        } catch (err) {
          reject(err);
        }
      });
    }

    const renderImage = () => {
      const img = new Image();
      img.src = props.src;
      loading.value = true;
      img.onload = async () => {
        const {
          width = img.width,
          height = img.height,
        } = props.operation || {};
        displayWidth.value = width || img.width;
        displayHeight.value = height || img.height;
        await renderImageToCanvas(img);
        loading.value = false;
      };
      img.onerror = () => {
        loading.value = false;
        console.error('Failed to load image:', props.src);
      };
    };

    onMounted(() => {
      renderImage(); // 初始渲染图片
    });

    // 监听 operation 的变化，重新渲染图片
    watch(
      () => props.operation,
      () => {
        if (canvasRef.value) {
          renderImage();
        }
      },
      { deep: true },
    );

    return () => (
      <div class="cutting-image-display pos-relative h-full w-full flex items-center justify-center overflow-hidden">
        <canvas
          class="transparent-bg"
          ref={canvasRef}
          style={getCanvasStyle(displayWidth.value, displayHeight.value)}
        />
        {loading.value && (
          <div class="pos-absolute left-0 top-0 h-full w-full flex items-center justify-center bg-FO-Container-Fill2">
            <div class="pos-absolute left-50% top-50% flex flex-col translate-x-[-50%] translate-y-[-50%] items-center gap-8px">
              <NIcon depth={3} size={32}>
                <Loading3QuartersOutlined class="animate-spin" />
              </NIcon>
              <NText class="ml-8px">加载中...</NText>
            </div>
          </div>
        )}
      </div>
    );
  },
});

export { DisplayCuttingImage };
