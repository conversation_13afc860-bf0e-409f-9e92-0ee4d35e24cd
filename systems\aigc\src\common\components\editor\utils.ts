import CustomError from '@/common/utils/custom-error';
import Konva from 'konva';

function createCursorDataURL(
  color: string,
  diameter: number,
  opacity: number = 1,
) {
  const svg = `
      <svg xmlns="http://www.w3.org/2000/svg" width="${diameter}" height="${diameter}" viewBox="0 0 24 24">
          <g fill="none" stroke="${color}" opacity="${opacity}" stroke-width="1.2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M8.56 3.69a9 9 0 0 0-2.92 1.95"></path>
              <path d="M3.69 8.56A9 9 0 0 0 3 12"></path>
              <path d="M3.69 15.44a9 9 0 0 0 1.95 2.92"></path>
              <path d="M8.56 20.31A9 9 0 0 0 12 21"></path>
              <path d="M15.44 20.31a9 9 0 0 0 2.92-1.95"></path>
              <path d="M20.31 15.44A9 9 0 0 0 21 12"></path>
              <path d="M20.31 8.56a9 9 0 0 0-1.95-2.92"></path>
              <path d="M15.44 3.69A9 9 0 0 0 12 3"></path>
          </g>
      </svg>`;
  return `data:image/svg+xml;base64,${btoa(svg)}`;
}

/**
 * 坐标点采样优化，避免密集场景绘制图形出现空白点
 */
function optimizeCoordinates(
  coordinates: number[],
  sampleInterval: number,
): number[] {
  const optimizedCoordinates: number[] = [];

  for (let i = 0; i < coordinates.length; i += 2) {
    const x = coordinates[i];
    const y = coordinates[i + 1];

    if (i === 0) {
      optimizedCoordinates.push(x, y);
    } else {
      const lastX = optimizedCoordinates[optimizedCoordinates.length - 2];
      const lastY = optimizedCoordinates[optimizedCoordinates.length - 1];
      const distance = Math.sqrt((x - lastX) ** 2 + (y - lastY) ** 2);

      if (distance >= sampleInterval) {
        optimizedCoordinates.push(x, y);
      }
    }
  }

  return optimizedCoordinates;
}

function copyCanvas(originalCanvas: HTMLCanvasElement): HTMLCanvasElement {
  // 创建一个新的canvas元素
  const newCanvas = document.createElement('canvas');

  // 设置新的canvas的尺寸与原始canvas相同
  newCanvas.width = originalCanvas.width;
  newCanvas.height = originalCanvas.height;

  // 获取两个canvas的2D上下文
  const originalContext = originalCanvas.getContext('2d');
  const newContext = newCanvas.getContext('2d');

  // 确保两个canvas上下文都存在
  if (originalContext && newContext) {
    // 将原始canvas的内容绘制到新的canvas上
    newContext.drawImage(originalCanvas, 0, 0);
  } else {
    throw new CustomError('Failed to get 2D context from canvas.', true);
  }

  // 返回新的canvas
  return newCanvas;
}

function isCanvasEmpty(canvas: HTMLCanvasElement): boolean {
  const context = canvas.getContext('2d');

  if (!context) {
    throw new CustomError('Failed to get 2D context from canvas.', true);
  }

  // 获取整个canvas的像素数据
  const imageData = context.getImageData(0, 0, canvas.width, canvas.height);

  // 检查是否所有像素都是透明的
  for (let i = 0; i < imageData.data.length; i += 4) {
    const alpha = imageData.data[i + 3]; // Alpha 通道值
    if (alpha !== 0) {
      // 如果发现非透明像素，表示canvas不为空
      return false;
    }
  }

  // 如果遍历完整个图像数据没有发现非透明像素，表示canvas为空
  return true;
}

/**
 * 旋转点坐标
 */
function rotatePoint(
  point: { x: number; y: number },
  rad: number,
): { x: number; y: number } {
  const rCos = Math.cos(rad);
  const rSin = Math.sin(rad);
  return {
    x: point.x * rCos - point.y * rSin,
    y: point.y * rCos + point.x * rSin,
  };
}
/**
 * 旋转节点到指定角度，保持中心点不变
 */
function rotateAroundCenter(params: {
  node: Konva.Node;
  angle: number;
}) {
  const { node, angle } = params;
  // 获取节点左上角相对于中心点的坐标
  const topLeft = { x: -node.width() * node.scaleX() / 2, y: -node.height() * node.scaleY() / 2 };
  const current = rotatePoint(topLeft, Konva.getAngle(node.rotation()));
  const rotated = rotatePoint(topLeft, Konva.getAngle(angle));
  const dx = rotated.x - current.x;
  const dy = rotated.y - current.y;

  node.rotation(angle);
  node.x(node.x() + dx);
  node.y(node.y() + dy);
}

/**
 * 按指定点旋转指定角度
 * @description center为以图像左上角为原点的xy坐标
 */
function rotateAroundPoint(params: {
  node: Konva.Node;
  angle: number;
  center: { x: number; y: number };
}) {
  const { node, angle, center } = params;
  const prevAngle = node.rotation();
  const rad = Konva.getAngle(angle - prevAngle);

  // 节点当前中心点坐标（以图像左上角为原点）
  const nodeCenter = {
    x: node.x(),
    y: node.y(),
  };

  // 以center为中心旋转
  const dx = nodeCenter.x - center.x;
  const dy = nodeCenter.y - center.y;
  const rotated = {
    x: dx * Math.cos(rad) - dy * Math.sin(rad),
    y: dx * Math.sin(rad) + dy * Math.cos(rad),
  };

  node.rotation(angle);
  node.x(center.x + rotated.x);
  node.y(center.y + rotated.y);
}

function applyFeatherToWholeCanvas(
  ctx: CanvasRenderingContext2D,
  width: number,
  height: number,
  featherSize: number,
) {
  const gradientCanvas = document.createElement('canvas');
  gradientCanvas.width = width;
  gradientCanvas.height = height;
  const gctx = gradientCanvas.getContext('2d')!;

  const gradient = gctx.createLinearGradient(0, 0, width, 0);
  const featherRatio = featherSize / width;

  gradient.addColorStop(0, 'rgba(0,0,0,0)');
  gradient.addColorStop(featherRatio, 'rgba(0,0,0,1)');
  gradient.addColorStop(1 - featherRatio, 'rgba(0,0,0,1)');
  gradient.addColorStop(1, 'rgba(0,0,0,0)');

  gctx.fillStyle = gradient;
  gctx.fillRect(0, 0, width, height);

  // 应用 alpha 渐变遮罩
  ctx.globalCompositeOperation = 'destination-in';
  ctx.drawImage(gradientCanvas, 0, 0);
  ctx.globalCompositeOperation = 'source-over';
}

/**
 * @description 创建离线canvas, 并根据裁剪区域和变换参数生成新的canvas
 */
function createOfflineCanvas(img?: HTMLImageElement, operations?: {
  rotate?: number;
  scale?: number;
  anchorX?: number;
  anchorY?: number;
  width?: number;
  height?: number;
  opacity?: number;
  useLinearGradient?: boolean;
}): HTMLCanvasElement {
  if (!img) {
    return document.createElement('canvas');
  }
  const {
    anchorX: x = 0,
    anchorY: y = 0,
    width: cropWidth,
    height: cropHeight,
    rotate = 0,
    scale = 1,
    opacity = 1,
  } = operations || {};
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');

  if (!ctx) {
    throw new Error('Failed to get canvas 2D context');
  }
  const width = cropWidth || img.width;
  const height = cropHeight || img.height;
  // canvas实际像素尺寸与裁切尺寸一致s
  canvas.width = width || img.width;
  canvas.height = height || img.height;
  ctx.imageSmoothingEnabled = true;
  ctx.imageSmoothingQuality = 'low';
  ctx.save();
  ctx.scale(scale, scale); // 缩放
  ctx.rotate((-rotate * Math.PI) / 180); // 反向旋转
  ctx.translate(-x / scale, -y / scale); // 将裁剪区域左上角对齐到canvas原点
  ctx.globalAlpha = opacity;
  ctx.drawImage(
    img,
    0,
    0,
  );
  ctx.restore();
  if (operations?.useLinearGradient) {
    const fadeWidth = 120; // 羽化区域宽度
    applyFeatherToWholeCanvas(ctx, canvas.width, canvas.height, fadeWidth);
  }
  return canvas;
}

/**
 * @description 检查一个节点是否是另一个节点的子孙节点或者等于该节点
 * @param node 要检查的节点
 * @param potentialAncestor 祖先节点
 */
function isDescendantOf(node: Konva.Node, potentialAncestor: Konva.Node): boolean {
  if (node === potentialAncestor) {
    return true; // 如果节点本身就是祖先节点
  }
  let current = node.getParent();
  while (current) {
    if (current === potentialAncestor) {
      return true;
    }
    current = current.getParent();
  }
  return false;
}

export { copyCanvas, createCursorDataURL, createOfflineCanvas, isCanvasEmpty, isDescendantOf, optimizeCoordinates, rotateAroundCenter, rotateAroundPoint };
