import { type CuttingLine, type ImageOperation, type RoleImage, type TaskImageItem, CuttingTaskType } from '@/models/cutting';
import { type PropType, computed, defineComponent, nextTick, ref, watch } from 'vue';
import { <PERSON><PERSON>utton, NDrawer, NDrawerContent, NIcon, NInputNumber, NPopover, NSlider, NSwitch, NText, NTooltip } from 'naive-ui';
import { type IEditorContext, Editor, EditorAction } from '@/common/components/editor';
import { getFileUrl, UploadScene } from '@/apis/oss.api';
import { useApiRequest, useAppTheme } from '@/common/hooks';
import { getForgeonColor } from '@hg-tech/forgeon-style';
import { BasicStrokeAdd, BasicStrokeConfig, BasicStrokeReset, BasicStrokeSubstract, Expand, Loading3QuartersOutlined } from '@/common/components/svg-icons';
import { debounce } from 'lodash';
import { SwitchWithClose } from '@/common/components/SwitchWithClose';
import CustomError from '@/common/utils/custom-error';

enum CuttingAction {
  Scale = 'scale',
  Rotate = 'rotate',
}

const CuttingEditModal = defineComponent({
  props: {
    visible: {
      type: Boolean as PropType<boolean>,
      default: false,
    },
    type: {
      type: String as PropType<CuttingTaskType>,
      default: CuttingTaskType.ARKNIGHTS_AVATAR,
    },
    images: {
      type: Object as PropType<TaskImageItem>,
      default: () => ({}),
    },
    lineGroup: {
      type: Array as PropType<CuttingLine[]>,
      default: () => [],
    },
    onSubmit: {
      type: Function as PropType<(id: string, operations: ImageOperation) => void>,
      default: () => {},
    },
    onUpdateVisible: {
      type: Function as PropType<(visible: boolean) => void>,
      default: () => {},
    },
    defaultMaleAvatar: {
      type: Object as PropType<RoleImage>,
      default: () => ({}),
    },
    defaultFemaleAvatar: {
      type: Object as PropType<RoleImage>,
      default: () => ({}),
    },
  },
  setup(props) {
    const { mutate: getFileUrlMutate,
    } = useApiRequest({
      request: getFileUrl,
      errorText: '获取裁剪图片预览失败',
    });
    const { currentTheme } = useAppTheme();
    const editorRef = ref<IEditorContext>();
    const basicImage = ref('');
    const holeWidth = ref<number>();
    const holeHeight = ref<number>();
    const offsetX = ref<number>();
    const offsetY = ref<number>();
    const currentAction = ref<CuttingAction>(CuttingAction.Scale);
    const rotate = ref(-(props.images.operations?.rotate || 0));
    const scale = ref(props.images.operations?.scale || 1);
    // 缩放蒙版区域比例，默认为空，通过updateBasicInfo计算初始值
    const maskScale = ref();
    const isShowLine = ref(true);
    const inImageLoading = ref(false);
    const defaultOperations = computed(() => props.images.operations);
    const currentOperations = computed(() => props.images.targetOperations || defaultOperations.value);
    const referenceImage = ref<RoleImage | null>(null);

    /**
     * 更新编辑器基础信息
     */
    const updateBasicInfo = async () => {
      const canvasWidth = editorRef.value?.plugins.instancePlugin.stage?.width() || 0;
      const canvasHeight = editorRef.value?.plugins.instancePlugin.stage?.height() || 0;
      // 实际截图区域大小
      holeWidth.value = currentOperations.value?.width || 0;
      holeHeight.value = currentOperations.value?.height || 0;
      if (!maskScale.value) {
        // 判断是否需要缩放蒙版区域, 如果画布的宽高小于1.2倍的截图区域，则缩放蒙版区域
        const maskHRatio = holeHeight.value / canvasHeight;
        const maskWRatio = holeWidth.value / canvasWidth;
        const maxMaskRatio = Math.max(maskHRatio, maskWRatio);
        if (maxMaskRatio > 0.8) {
          // 缩放到画布80%大小
          const scale = Math.min(canvasWidth / holeWidth.value, canvasHeight / holeHeight.value) * 0.8;
          maskScale.value = scale;
          holeWidth.value = holeWidth.value * scale;
          holeHeight.value = holeHeight.value * scale;
        } else {
          maskScale.value = 1;
        }
      } else {
        holeWidth.value = (currentOperations.value?.width || 0) * maskScale.value;
        holeHeight.value = (currentOperations.value?.height || 0) * maskScale.value;
      }
      offsetX.value = (canvasWidth - holeWidth.value) / 2;
      offsetY.value = (canvasHeight - holeHeight.value) / 2;
      rotate.value = -(currentOperations.value?.rotate || 0);
    };

    const setMask = () => {
      editorRef.value?.plugins.instancePlugin.setCanvasMask({
        color: getForgeonColor('ContainerBackground', currentTheme.value),
        opacity: 0.7,
        holeConfig: {
          width: holeWidth.value || 0,
          height: holeHeight.value || 0,
          offsetX: offsetX.value || 0,
          offsetY: offsetY.value || 0,
          image: referenceImage.value?.ossPath,
        },
      });
    };

    const setLine = () => {
      editorRef.value?.plugins.instancePlugin.setLineGroup(props.lineGroup.map((line) => {
        return {
          ...line,
          x: line.x * maskScale.value + (offsetX.value || 0),
          y: line.y * maskScale.value + (offsetY.value || 0),
          color: getForgeonColor('ContentIcon2', currentTheme.value),
        };
      }));
      editorRef.value?.plugins.instancePlugin.setGuideLineVisible(isShowLine.value);
    };

    /**
     * 设置底图位置
     */
    const setImagePosition = async (operation: ImageOperation) => {
      const anchorX = operation.anchorX || 0;
      const anchorY = operation.anchorY || 0;
      const s = operation.scale || 1;
      // 逆推底图基于原点的坐标
      const rad = ((operation.rotate ?? 0) * Math.PI) / 180;
      // 计算蒙版原点相对于底图原点的偏移量，考虑蒙版缩放比例
      const dx = anchorX * maskScale.value;
      const dy = anchorY * maskScale.value;
      // 计算底图左上角坐标（逆向 anchor 变换，注意 rotate 方向）
      const x = (offsetX.value ?? 0) - (dx * Math.cos(-rad) - dy * Math.sin(-rad));
      const y = (offsetY.value ?? 0) - (dx * Math.sin(-rad) + dy * Math.cos(-rad));
      await editorRef.value?.plugins.instancePlugin.setBaseImage({
        id: props.images.id,
        name: props.images.name,
        url: basicImage.value,
        status: 'finished',
      }, {
        x,
        y,
        scale: s * maskScale.value,
        angle: -operation?.rotate || 0,
      });
      setMask();
      rotate.value = -operation.rotate;
      scale.value = s * maskScale.value;
    };

    const getCurrentOperations = () => {
      const currentPositionX = editorRef.value?.plugins.instancePlugin.currentShape?.position().x || 0;
      const currentPositionY = editorRef.value?.plugins.instancePlugin.currentShape?.position().y || 0;
      const r = editorRef.value?.plugins.instancePlugin.currentShape?.rotation() || 0;
      const relX = (offsetX.value ?? 0) - currentPositionX;
      const relY = (offsetY.value ?? 0) - currentPositionY;
      const rad = (-r * Math.PI) / 180;
      // 计算蒙版原点相对于以底图原点为坐标系原点的左边值
      const dx = relX * Math.cos(rad) - relY * Math.sin(rad);
      const dy = relX * Math.sin(rad) + relY * Math.cos(rad);
      // 计算提交的操作
      const resOperations: ImageOperation = {
        width: props.images.operations?.width || 0,
        height: props.images.operations?.height || 0,
        rotate: -r,
        scale: (editorRef.value?.plugins.instancePlugin.scale || 1) / maskScale.value,
        anchorX: (dx || 0) / maskScale.value,
        anchorY: (dy || 0) / maskScale.value,
      };
      return resOperations;
    };

    const onSubmit = () => {
      props.onSubmit(props.images.id, getCurrentOperations());
      props.onUpdateVisible(false);
    };

    /**
     * 图片底图数值调整
     */
    const onImageChange = (val: number, type: CuttingAction) => {
      if (type === CuttingAction.Rotate) {
        editorRef.value?.plugins.instancePlugin.setImageAngle(val);
        rotate.value = val;
      } else {
        editorRef.value?.plugins.instancePlugin.setKonvaNodeRatio({
          ratio: val * maskScale.value,
        });
      }
    };

    /**
     * 遮罩框调整
     */
    const onMaskScale = async (val: number | null) => {
      if (!val) {
        return;
      }
      editorRef.value?.plugins.instancePlugin.setKonvaNodeRatio({
        ratio: (val / 100) * (scale.value / (maskScale.value || 1)),
      });
      maskScale.value = (val as number) / 100;
      updateBasicInfo();
      setMask();
      setLine();
    };
    const onMaskScaleChange = debounce(async (val: number | null) => {
      const currentOperations = getCurrentOperations();
      onMaskScale(val);
      await nextTick();
      await setImagePosition(currentOperations);
    }, 100);

    // 初始化编辑器
    watch(() => props.visible, async () => {
      referenceImage.value = null;
      // 根据oss获取图片地址
      if (props.images && props.images.basicImage?.url && props.visible && currentOperations.value) {
        try {
          const { url } = await getFileUrlMutate({
            name: props.images.name,
            scene: UploadScene.CropImage,
          });
          basicImage.value = url;
          updateBasicInfo();
          inImageLoading.value = true;
          await setImagePosition(currentOperations.value);
          inImageLoading.value = false;
          setTimeout(() => {
            editorRef.value?.plugins.eventPlugin.switchAction(EditorAction.Mover);
            setLine();
            setMask();
          }, 200);
        } catch (error) {
          throw new CustomError(`获取裁剪图片预览失败:${error}`, false);
        } finally {
          inImageLoading.value = false;
        }
      }
    }, {
      deep: true,
      immediate: true,
    });

    watch([() => editorRef.value?.plugins.instancePlugin.scale, maskScale], (val, oldVal) => {
      const [imageScale, mScale] = val;
      const [oldScale, oldMScale] = oldVal;
      if (oldScale !== imageScale) {
        scale.value = (imageScale || 1) / (mScale === oldMScale ? 1 : mScale);
      }
    });

    watch(() => isShowLine.value, (newVal) => {
      editorRef.value?.plugins.instancePlugin.setGuideLineVisible(newVal);
    });

    watch(() => props.type, (newVal, oldVal) => {
      if (newVal !== oldVal) {
        maskScale.value = undefined;
        currentAction.value = CuttingAction.Scale;
      }
    });

    const renderSliderMark = (val: string, onClick: () => void) => {
      return <div class="FO-Font-R12 c-FO-Content-Icon2" onClick={onClick}>{val}</div>;
    };

    const renderActions = () => {
      return (
        <div class="mx-auto flex items-center gap-24px rd-12px bg-FO-Container-Fill1 p-16px">
          <div class="flex-c-start flex gap-8px">
            <NButton
              class={['px-8px', currentAction.value === CuttingAction.Scale ? 'btn-fill-secondary' : 'btn-full-default']}
              onClick={() => currentAction.value = CuttingAction.Scale}
              renderIcon={() => <Expand />}
              secondary
              size="small"
            />
            {[CuttingTaskType.ARKNIGHTS_AVATAR, CuttingTaskType.ARKNIGHTS_HALF].includes(props.type) && (
              <NButton
                class={['px-8px', currentAction.value === CuttingAction.Rotate ? 'btn-fill-secondary' : 'btn-full-default']}
                onClick={() => currentAction.value = CuttingAction.Rotate}
                renderIcon={() => <BasicStrokeReset />}
                secondary
                size="small"
              />
            )}
          </div>
          <div class="flex-c-start gap-12px">
            <div class="mb--20px w-300px">
              {
                currentAction.value === CuttingAction.Scale
                  ? (
                    <NSlider
                      formatTooltip={(val) => `${(val).toFixed(3)}倍`}
                      marks={{
                        1: () => renderSliderMark('1倍', () => onImageChange(1, CuttingAction.Scale)),
                        15: () => renderSliderMark('15倍', () => onImageChange(15, CuttingAction.Scale)),
                        30: () => renderSliderMark('30倍', () => onImageChange(30, CuttingAction.Scale)),
                      }}
                      max={30}
                      min={0}
                      onUpdate:value={(val) => onImageChange(val, CuttingAction.Scale)}
                      step={0.001}
                      value={scale.value / maskScale.value} // 保持 scale 在 0-30 范围内
                    />
                  )
                  : (
                    <NSlider
                      formatTooltip={(val) => `${(val - 180).toFixed(3)}°`}
                      marks={{
                        0: () => renderSliderMark('-180°', () => onImageChange(-180, CuttingAction.Rotate)),
                        90: () => renderSliderMark('-90°', () => onImageChange(-90, CuttingAction.Rotate)),
                        180: () => renderSliderMark('0°', () => onImageChange(0, CuttingAction.Rotate)),
                        270: () => renderSliderMark('90°', () => onImageChange(90, CuttingAction.Rotate)),
                        360: () => renderSliderMark('180°', () => onImageChange(180, CuttingAction.Rotate)),
                      }}
                      max={360}
                      min={0}
                      onUpdate:value={(val) => onImageChange(val - 180, CuttingAction.Rotate)}
                      step={0.001}
                      value={rotate.value + 180}
                    />
                  )
              }
            </div>
            <NTooltip contentClass="w-200px" placement="top">
              {{
                default: () => `精细调整${currentAction.value === CuttingAction.Scale ? '缩放' : '旋转'}值，可focus当前输入框后通过↑、↓键进行微调，${
                  currentAction.value === CuttingAction.Scale ? '或通过滚轮进行缩放，缩放范围在1-30倍之间。' : '旋转范围在-180°到180°之间。'
                }`,
                trigger: () => (
                  <NInputNumber
                    class="w-100px"
                    format={(val) => (val as number).toFixed(3)}
                    max={currentAction.value === CuttingAction.Scale ? 30 : 180}
                    min={currentAction.value === CuttingAction.Scale ? 0 : -180}
                    onUpdateValue={(val) => onImageChange(val as number, currentAction.value)}
                    showButton={false}
                    size="small"
                    step={currentAction.value === CuttingAction.Scale ? 0.001 : 0.001}
                    value={currentAction.value === CuttingAction.Scale ? scale.value / maskScale.value : rotate.value}
                  >
                    {{
                      suffix: () => (currentAction.value === CuttingAction.Scale ? '倍' : '°'),
                    }}
                  </NInputNumber>
                ),
              }}
            </NTooltip>

          </div>
          <div>
            <NTooltip contentClass="w-200px" placement="top">
              {{
                default: () => '重置当前操作到上一次编辑状态，如不存在则重置为AI切图数据',
                trigger: () => (
                  <NButton
                    class="btn-fill-default px-12px"
                    onClick={() => {
                      setImagePosition(currentOperations.value!);
                    }}
                    secondary
                    size="small"
                  >
                    重置
                  </NButton>
                ),
              }}
            </NTooltip>

          </div>
        </div>
      );
    };

    const renderContent = () => {
      return (
        <div class="h-full w-full flex flex-col gap-24px">
          <div class="pos-relative relative h-full w-full flex-1">
            <Editor
              allowWheel
              class="h-full w-full overflow-hidden rd-12px"
              ref={editorRef}
              transparantBg={false}
            />
            {inImageLoading.value && (
              <div class="absolute left-50% top-50% flex flex-col translate-x-[-50%] translate-y-[-50%] items-center gap-8px">
                <NIcon depth={3} size={32}>
                  <Loading3QuartersOutlined class="animate-spin" />
                </NIcon>
                <NText class="ml-8px">正在加载图片...</NText>
              </div>
            )}
            <div class="absolute bottom-68px left-50% flex translate-x-[-50%] items-center">
              {renderActions()}
            </div>
          </div>
          <div class="action-bar flex-c-between gap-12px">
            <NTooltip contentClass="w-200px" placement="top">
              {{
                default: () => '重置到AI切图的基本数据状态',
                trigger: () => (
                  <NButton
                    class="btn-fill-default"
                    onClick={async () => {
                      await onMaskScale(maskScale.value * 100);
                      setImagePosition(defaultOperations.value!);
                    }}
                    secondary
                    size="small"
                  >
                    重置所有操作
                  </NButton>
                ),
              }}
            </NTooltip>

            <div class="flex items-center gap-8px">
              <NButton
                class="btn-fill-default px-8px"
                onClick={() => {
                  onMaskScaleChange(maskScale.value * 100 - 10);
                }}
                renderIcon={() => <BasicStrokeSubstract />}
                secondary
                size="small"
              />
              <NInputNumber
                class="w-70px"
                format={(val) => (val as number).toFixed(0)}
                max={300}
                min={1}
                onUpdateValue={(val) => {
                  onMaskScaleChange(val as number);
                }}
                showButton={false}
                step={1}
                updateValueOnInput={true}
                value={maskScale.value * 100}
              >
                {{
                  suffix: () => '%',
                }}
              </NInputNumber>
              <NButton
                class="btn-fill-default px-8px"
                onClick={() => {
                  onMaskScaleChange(maskScale.value * 100 + 10);
                }}
                renderIcon={() => <BasicStrokeAdd />}
                secondary
                size="small"
              />
            </div>

            <div class="flex items-center gap-8px">
              <NText>
                模板和参考线
              </NText>
              <NPopover
                placement="top-end"
                trigger="click"
              >
                {{
                  default: () => (
                    <div class="flex flex-col gap-12px p-12px">
                      <div class="flex-c-between">
                        <NText>参考线</NText>
                        <NSwitch v-model:value={isShowLine.value} />
                      </div>
                      <div class="flex-c-between gap-12px">
                        <NText>参考角色</NText>
                        <SwitchWithClose
                          onChange={async (value) => {
                            if (value === null) {
                              referenceImage.value = null;
                            } else {
                              referenceImage.value = (value === 'male'
                                ? props.defaultMaleAvatar
                                : props.defaultFemaleAvatar) as RoleImage;
                            }
                            setMask();
                          }}
                          options={[
                            { label: '男', value: 'male' },
                            { label: '女', value: 'female' },
                          ]}
                          value={referenceImage.value ? referenceImage.value === props.defaultMaleAvatar ? 'male' : 'female' : null}
                        />
                      </div>
                    </div>
                  ),
                  trigger: () => (
                    <NTooltip>
                      {{
                        default: () => '设置参考和模板',
                        trigger: () => (
                          <NButton
                            class={['px-6px', isShowLine.value || referenceImage.value ? 'btn-fill-secondary' : 'btn-fill-default']}
                            renderIcon={() => <BasicStrokeConfig />}
                            secondary
                            size="small"
                          />
                        ),
                      }}
                    </NTooltip>
                  ),
                }}
              </NPopover>
            </div>
          </div>
        </div>
      );
    };

    return () => (
      <NDrawer
        autoFocus={false}
        class="m-24px rd-16px!"
        close-on-esc={false}
        maskClosable={false}
        onUpdateShow={props.onUpdateVisible}
        placement="right"
        show={props.visible}
        width="924px"
      >
        <NDrawerContent body-content-class="p-24px! h-full" closable native-scrollbar={false}>
          {{
            default: () => renderContent(),
            header: () => (
              <NText class="text-lg font-bold">
                编辑图像
              </NText>
            ),
            footer: () => (
              <div class="w-full flex justify-end gap-8px">
                <NButton class="btn-fill-default px-24px" onClick={() => props.onUpdateVisible(false)} secondary>取消</NButton>
                <NButton class="btn-fill-primary px-24px" onClick={onSubmit} type="primary">确认</NButton>
              </div>
            ),
          }}
        </NDrawerContent>
      </NDrawer>
    );
  },
});
export {
  CuttingEditModal,
};
