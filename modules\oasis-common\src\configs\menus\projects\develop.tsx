import type { ICommonMenuItem } from '../types';
import { ForgeonTitleMap, OutSideRoutePath, PlatformEnterPoint, PlatformRoutePath } from '../../route-path';
import { PermissionPoint } from '../../auth/permission';

import MenuDevGuard from '../../../assets/svg/menu/MenuDevGuard.svg?component';
import MenuDevGuardActive from '../../../assets/svg/menu/MenuDevGuardActive.svg?component';
import MenuGamePackage from '../../../assets/svg/menu/MenuGamePackage.svg?component';
import MenuGamePackageActive from '../../../assets/svg/menu/MenuGamePackageActive.svg?component';
import MenuToolkit from '../../../assets/svg/menu/MenuToolkit.svg?component';
import MenuToolkitActive from '../../../assets/svg/menu/MenuToolkitActive.svg?component';
import MenuP4Training from '../../../assets/svg/menu/MenuP4Training.svg?component';
import MenuP4TrainingActive from '../../../assets/svg/menu/MenuP4TrainingActive.svg?component';
import MenuOasis from '../../../assets/svg/menu/MenuOasis.svg?component';
import MenuOasisActive from '../../../assets/svg/menu/MenuOasisActive.svg?component';
import MenuMessageCenter from '../../../assets/svg/menu/MenuMessageCenter.svg?component';
import MenuMessageCenterActive from '../../../assets/svg/menu/MenuMessageCenterActive.svg?component';
import MenuWebHookPlatform from '../../../assets/svg/menu/MenuWebHookPlatform.svg?component';
import MenuWebHookPlatformActive from '../../../assets/svg/menu/MenuWebHookPlatformActive.svg?component';
import MenuOutsourcingPlatform from '../../../assets/svg/menu/MenuOutsourcingPlatform.svg?component';
import MenuOutsourcingPlatformActive from '../../../assets/svg/menu/MenuOutsourcingPlatformActive.svg?component';
import MenuTrackingAnalysis from '../../../assets/svg/menu/MenuTrackingAnalysis.svg?component';
import MenuTrackingAnalysisActive from '../../../assets/svg/menu/MenuTrackingAnalysisActive.svg?component';
import MenuPermissionCenter from '../../../assets/svg/menu/MenuPermissionCenter.svg?component';
import MenuPermissionCenterActive from '../../../assets/svg/menu/MenuPermissionCenterActive.svg?component';
import MenuConflux from '../../../assets/svg/menu/MenuConflux.svg';
import MenuConfluxActive from '../../../assets/svg/menu/MenuConfluxActive.svg';

export const DevelopMenuConfig: ICommonMenuItem[] = [
  {
    key: PlatformEnterPoint.DevGuard,
    name: PlatformEnterPoint.DevGuard,
    title: ForgeonTitleMap[PlatformEnterPoint.DevGuard],
    svgIcon: () => <MenuDevGuard />,
    activeIcon: () => <MenuDevGuardActive />,
    children: [
      {
        key: PlatformEnterPoint.P4Depots,
        name: PlatformEnterPoint.P4Depots,
        path: PlatformRoutePath.P4Depots,
        title: ForgeonTitleMap[PlatformEnterPoint.P4Depots],
        prefix: [PlatformRoutePath.P4Depots, PlatformRoutePath.ProjectsManagement],
        permissionDeclare: {
          any: [PermissionPoint.P4Depots],
        },
      },
      {
        key: PlatformEnterPoint.CommitCenter,
        name: PlatformEnterPoint.CommitCenter,
        path: PlatformRoutePath.CommitCenter,
        title: ForgeonTitleMap[PlatformEnterPoint.CommitCenter],
        permissionDeclare: {
          any: [],
        },
      },
      {
        key: PlatformEnterPoint.Gitlab,
        name: PlatformEnterPoint.Gitlab,
        path: PlatformRoutePath.Gitlab,
        title: ForgeonTitleMap[PlatformEnterPoint.Gitlab],
        permissionDeclare: {
          any: [PermissionPoint.Gitlab],
        },
      },
      {
        key: PlatformEnterPoint.P4MemberManagement,
        name: PlatformEnterPoint.P4MemberManagement,
        path: PlatformRoutePath.P4MemberManagement,
        title: ForgeonTitleMap[PlatformEnterPoint.P4MemberManagement],
        permissionDeclare: {
          any: [PermissionPoint.P4MemberManagement],
        },
      },
      {
        key: PlatformEnterPoint.P4CustomGroupManagement,
        name: PlatformEnterPoint.P4CustomGroupManagement,
        path: PlatformRoutePath.P4CustomGroupManagement,
        title: ForgeonTitleMap[PlatformEnterPoint.P4CustomGroupManagement],
        prefix: [PlatformRoutePath.P4CustomGroupManagement, PlatformRoutePath.P4LdapGroupManagement],
        permissionDeclare: {
          any: [PermissionPoint.P4CustomGroupManagement],
        },
      },
    ],
    description: '游戏资产仓库',
  },
  {
    key: PlatformEnterPoint.GamePackage,
    name: PlatformEnterPoint.GamePackage,
    path: PlatformRoutePath.GamePackage,
    title: '游戏包体中心',
    svgIcon: () => <MenuGamePackage />,
    activeIcon: () => <MenuGamePackageActive />,
    permissionDeclare: {
      any: [PermissionPoint.GamePackage],
    },
    description: '游戏包体下载',
  },
  {
    key: PlatformEnterPoint.Toolkit,
    name: PlatformEnterPoint.Toolkit,
    path: PlatformRoutePath.Toolkit,
    title: '工具中心',
    svgIcon: () => <MenuToolkit />,
    activeIcon: () => <MenuToolkitActive />,
    permissionDeclare: {
      any: [PermissionPoint.Toolkit],
    },
    prefix: [PlatformRoutePath.Toolkit, PlatformRoutePath.ToolkitDetail, PlatformRoutePath.ToolkitPackageSettings],
    description: '常用开发工具下载',
  },
  {
    key: PlatformEnterPoint.P4Training,
    name: PlatformEnterPoint.P4Training,
    path: PlatformRoutePath.P4Training,
    title: 'P4学习',
    svgIcon: () => <MenuP4Training />,
    activeIcon: () => <MenuP4TrainingActive />,
    permissionDeclare: {
      any: [PermissionPoint.P4Trains, PermissionPoint.P4Onboarding, PermissionPoint.TCP4T, PermissionPoint.P4Pass],
    },
    prefix: [PlatformRoutePath.P4Trains, PlatformRoutePath.P4Onboarding, PlatformRoutePath.TCP4T, PlatformRoutePath.P4Pass],
    description: '学习P4教程',
  },
  {
    key: PlatformEnterPoint.Oasis,
    name: PlatformEnterPoint.Oasis,
    path: PlatformRoutePath.Oasis,
    title: 'Oasis配置',
    svgIcon: () => <MenuOasis />,
    activeIcon: () => <MenuOasisActive />,
    permissionDeclare: {
      any: [PermissionPoint.Oasis, PermissionPoint.InstructionCombinations, PermissionPoint.ToolNavigations, PermissionPoint.GroupChat, PermissionPoint.ToolGroupChat],
    },
    prefix: [PlatformRoutePath.ToolNavigations, PlatformRoutePath.Instructions, PlatformRoutePath.GroupChat, PlatformRoutePath.ToolGroupChat,
    ],
    description: 'Oasis配置后台',
  },
  {
    key: PlatformEnterPoint.MessageCenter,
    name: PlatformEnterPoint.MessageCenter,
    path: PlatformRoutePath.MessageTemplate,
    title: '消息发送中心',
    svgIcon: () => <MenuMessageCenter />,
    activeIcon: () => <MenuMessageCenterActive />,
    permissionDeclare: {
      any: [PermissionPoint.MessageTemplate],
    },
    description: '定时消息发送配置',
  },
  {
    key: PlatformEnterPoint.WebHookPlatform,
    name: PlatformEnterPoint.WebHookPlatform,
    path: OutSideRoutePath.WebHookPlatform,
    title: '消息处理中心',
    svgIcon: () => <MenuWebHookPlatform />,
    activeIcon: () => <MenuWebHookPlatformActive />,
    permissionDeclare: {
      any: [PermissionPoint.WebHookPlatform],
    },
    description: '飞书消息收集与发送',
  },
  {
    key: PlatformEnterPoint.OutsourcingPlatform,
    name: PlatformEnterPoint.OutsourcingPlatform,
    path: OutSideRoutePath.OutsourcingPlatform,
    title: '鹰角众包平台',
    svgIcon: () => <MenuOutsourcingPlatform />,
    activeIcon: () => <MenuOutsourcingPlatformActive />,
    permissionDeclare: {
      any: [PermissionPoint.OutsourcingPlatform],
    },
    description: '外包仓库权限管理平台',
  },
  {
    key: PlatformEnterPoint.TrackingAnalysis,
    name: PlatformEnterPoint.TrackingAnalysis,
    path: PlatformRoutePath.Tracking,
    title: '埋点分析平台',
    svgIcon: () => <MenuTrackingAnalysis />,
    activeIcon: () => <MenuTrackingAnalysisActive />,
    permissionDeclare: {
      any: [PermissionPoint.TrackingAnalysis],
    },
    prefix: [PlatformRoutePath.TrackingAnalysis, PlatformRoutePath.TrackingAnalysisSettings],
    description: '研发工具与平台埋点数据',
  },
  {
    key: PlatformEnterPoint.SysPermissionCenter,
    name: PlatformEnterPoint.SysPermissionCenter,
    path: PlatformRoutePath.SysPermissionCenter,
    title: '权限管理中心',
    svgIcon: () => <MenuPermissionCenter />,
    activeIcon: () => <MenuPermissionCenterActive />,
    prefix: [PlatformRoutePath.SysPermissionCenter],
    description: '对应用权限进行统一管理',
  },
  {
    key: PlatformEnterPoint.Conflux,
    name: PlatformEnterPoint.Conflux,
    path: PlatformRoutePath.Conflux,
    title: ForgeonTitleMap[PlatformEnterPoint.Conflux],
    svgIcon: () => <MenuConflux />,
    activeIcon: () => <MenuConfluxActive />,
    permissionDeclare: {
      scope: PlatformEnterPoint.Conflux,
      all: ['ReadMerge', 'ViewRule'],
    },
    prefix: [PlatformRoutePath.Conflux],
  },
];
