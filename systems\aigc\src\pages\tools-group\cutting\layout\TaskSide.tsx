import { type FileUploaderExpose, FileUploader } from '@/common/components/file-uploader';
import { type CuttingTask, type CuttingTaskType, CuttingTaskStatus, ImageHandlingState, TaskImageItem } from '@/models/cutting';
import { type ScrollbarInst, NButton, NCollapse, NCollapseItem, NIcon, NPopconfirm, NScrollbar, NTooltip } from 'naive-ui';
import { type PropType, computed, defineComponent, nextTick, ref, watch } from 'vue';
import { TaskItem } from '../components/TaskItem';
import { ArrowForwardIosRound, BasicFillAISlice, BasicStrokeCross, BasicStrokeDelete, SystemFillClose } from '@/common/components/svg-icons';
import { deserialize } from '@lancercomet/suntori';
import { UploadScene } from '@/apis/oss.api';

const MAX_PREVIEW_IMAGES = 99; // 最大预览图片数量

const CuttingTaskSide = defineComponent({
  props: {
    taskTypeList: {
      type: Array as PropType<CuttingTaskType[]>,
      required: true,
    },
    list: {
      type: Array as PropType<CuttingTask[]>,
      required: true,
    },
    /**
     * 当前激活的任务ID
     */
    currentKey: {
      type: String as PropType<string>,
      default: '',
    },
    isDrawing: {
      type: Boolean as PropType<boolean>,
      default: false,
    },
    /**
     * 当前激活的图片oss path，用于高亮显示
     */
    activeImageKey: {
      type: String as PropType<string>,
      default: '',
    },
    onCreateNewTask: {
      type: Function as PropType<() => void>,
      default: () => {},
    },
    onRemoveTaskItem: {
      type: Function as PropType<(taskId: string) => void>,
      default: () => {},
    },
    onSubmit: {
      type: Function as PropType<(taskId: string) => void>,
      default: () => {},
    },
    /**
     * 取消当前裁切任务
     */
    onCancel: {
      type: Function as PropType<(taskId: string) => void>,
      default: () => {},
    },
    onUpdateKey: {
      type: Function as PropType<(key: string) => void>,
      default: () => {},
    },
    /**
     * 更新当前任务的图片列表
     */
    onUpdateImages: {
      type: Function as PropType<(index: number, images: TaskImageItem[]) => void>,
      default: () => {},
    },
    /**
     * 更新当前激活的图片key
     */
    onUpdateActiveImageKey: {
      type: Function as PropType<(taskId: string, image: TaskImageItem) => void>,
      default: () => {},
    },
    onUpdateTaskType: {
      type: Function as PropType<(type: CuttingTaskType) => void>,
      default: () => {},
    },
  },
  setup(props) {
    const uploaderRef = ref<FileUploaderExpose>();
    const currentExpanded = ref<string[]>([props.currentKey]);
    const scrollbarRef = ref<ScrollbarInst>();
    const containerRef = ref<HTMLElement | null>(null);
    const currentTask = computed(() => {
      return props.list.find((task) => task.id === props.currentKey);
    });
    const submitDisabled = computed(() => {
      return !props.currentKey
        || currentTask.value?.status !== CuttingTaskStatus.PENDING
        || currentTask.value?.images.length === 0
        || currentTask.value?.images.some((item) => item.basicImage?.status !== 'finished');
    });
    const submitLoading = computed(() => {
      return Boolean(props.currentKey && currentTask.value?.status === CuttingTaskStatus.PROCESSING);
    });

    watch(() => props.currentKey, (newKey) => {
      if (newKey) {
        currentExpanded.value = [newKey];
      }
    });

    const onExpandedNamesChange = (names: string[]) => {
      if (names.length === 0 && props.list.length === 1) {
        return;
      }
      currentExpanded.value = names;
      if (names.length !== 0) {
        props.onUpdateKey(names[0]);
      }
    };

    const onRemoveImage = (itemId: string) => {
      const targetTaskIndex = props.list.findIndex((t) => t.id === props.currentKey);
      if (targetTaskIndex !== -1) {
        const task = props.list[targetTaskIndex];
        props.onUpdateImages(targetTaskIndex, task.images.filter((img) => img.id !== itemId));
      }
    };

    const onScrollToActiveImage = (top: number) => {
      if (scrollbarRef.value) {
        const parent = containerRef.value?.parentElement?.parentElement?.getBoundingClientRect().top || 0;
        const scrollTop = containerRef.value?.parentElement?.parentElement?.scrollTop || 0;
        scrollbarRef.value?.scrollTo({ top: top - parent + scrollTop - 150, behavior: 'smooth' });
      }
    };

    const renderActions = (task: CuttingTask) => {
      return (
        <div onClick={(e) => e.stopPropagation()}>
          {[CuttingTaskStatus.PENDING].includes(task.status) && task.images.length > 0 && (
            <NPopconfirm
              contentClass="max-w-300px pt-4px pb-8px px-2px"
              negativeButtonProps={{
                size: 'tiny',
                secondary: true,
              }}
              onPositiveClick={() => {
                task.images = [];
              }}
              placement="top-start"
              positiveButtonProps={{
                size: 'tiny',
              }}
            >
              {{
                default: () => <span class="mb-12px">当前已上传图片会被清空（不影响已完成切图），确认要删除已上传图片吗？</span>,
                trigger: () => (
                  <NTooltip>
                    {{
                      default: () => '清空已上传图片',
                      trigger: () => (
                        <NButton class="btn-fill-text px-8px" secondary size="small">
                          <NIcon class="c-FO-Content-Icon1" size={16}>
                            <BasicStrokeDelete />
                          </NIcon>
                        </NButton>
                      ),
                    }}
                  </NTooltip>
                ),
              }}
            </NPopconfirm>
          )}
          {[CuttingTaskStatus.CANCELED, CuttingTaskStatus.FINISHED].includes(task.status) && (
            <NPopconfirm
              contentClass="max-w-300px pt-4px pb-8px px-2px"
              negativeButtonProps={{
                size: 'tiny',
                secondary: true,
              }}
              onPositiveClick={() => {
                props.onRemoveTaskItem(task.id);
              }}
              placement="top-start"
              positiveButtonProps={{
                size: 'tiny',
              }}
            >
              {{
                default: () => <span>当前已处理图片会被清空，确认要删除已处理图片吗？</span>,
                trigger: () => (
                  <NTooltip>
                    {{
                      default: () => '删除当前已完成的切图',
                      trigger: () => (
                        <NButton class="btn-fill-text px-8px" secondary size="small">
                          <NIcon class="c-FO-Content-Icon1" size={16}>
                            <BasicStrokeDelete />
                          </NIcon>
                        </NButton>
                      ),
                    }}
                  </NTooltip>
                ),
              }}
            </NPopconfirm>
          )}
          {task.status === CuttingTaskStatus.PROCESSING && (
            <NPopconfirm
              contentClass="max-w-300px pt-4px pb-8px px-2px"
              negativeButtonProps={{
                size: 'tiny',
                secondary: true,
              }}
              onPositiveClick={() => {
                props.onCancel(task.id);
              }}
              placement="top-start"
              positiveButtonProps={{
                size: 'tiny',
              }}
            >
              {{
                default: () => <span>确认取消裁切任务吗？</span>,
                trigger: () => (
                  <NTooltip>
                    {{
                      default: () => '取消裁切任务',
                      trigger: () => (
                        <NButton class="btn-fill-text px-8px" secondary size="small">
                          <NIcon class="c-FO-Content-Icon1" size={16}>
                            <BasicStrokeCross />
                          </NIcon>
                        </NButton>
                      ),
                    }}
                  </NTooltip>
                ),
              }}
            </NPopconfirm>
          )}
        </div>
      );
    };

    return () => (
      <div class="pos-relative h-full w-380px flex flex-col rd-lg bg-FO-Container-Fill1 p-24px">
        <div class="c-text-gray1 FO-Font-B16 mb-12px">
          <span class="mr-4px c-FO-Brand-Primary-Default">Step 1.</span>
          添加图片资源
        </div>
        <div class="mb-12px">
          <FileUploader
            accept="image/png"
            class="bg-FO-Container-Fill2"
            compressDisabled
            containerStyle={{
              width: '100%',
              height: '100px',
              borderRadius: '12px',
            }}
            maxCount={MAX_PREVIEW_IMAGES}
            maxSize={200}
            multiple
            nameFormat={(name: string, uuid: string, extension: string) => {
              return `${name}_【${uuid}】.${extension}`;
            }}
            onBeforeUpload={async (file) => {
              if (props.list.every((task) => task.status !== CuttingTaskStatus.PENDING)) {
                props.onCreateNewTask();
              }
              await nextTick();
              const penddingTask = props.list.find((task) => task.status === CuttingTaskStatus.PENDING);
              if (!penddingTask) {
                return false; // 如果没有待处理任务，则不允许上传
              }
              // 提前将图片添加到当前任务图片中
              const targetTaskIndex = props.list.findIndex((task) => task.id === penddingTask.id);
              if (targetTaskIndex !== -1) {
                props.onUpdateImages(targetTaskIndex, [
                  ...penddingTask?.images || [],
                  deserialize({
                    id: file.id,
                    name: file.name, // 文件本名，后续替换为唯一名
                    basicImage: file,
                    state: ImageHandlingState.PENDING,
                  }, TaskImageItem),
                ]);
                return true; // 返回 true 允许上传
              } else {
                return false; // 如果没有找到当前任务，则不允许上传
              }
            }}
            onFileUploadDone={(files) => {
              // 上传完成后更新onBeforeUpload中添加的图片，通过id找到对应的图片并更新
              files.forEach((file) => {
                const penddingTask = props.list.find((task) => task.status === CuttingTaskStatus.PENDING);
                if (!penddingTask) {
                  return; // 如果没有当前任务，则不处理
                }
                const targetTaskIndex = props.list.findIndex((task) => task.id === penddingTask.id);
                const index = penddingTask?.images.findIndex((img) => img.id === file.id);
                props.onUpdateImages(targetTaskIndex, penddingTask.images.map((item, idx) => {
                  if (idx === index) {
                    item.basicImage = file;
                    item.name = file.name; // 更新唯一名
                  }
                  return item;
                }));
              });
              uploaderRef.value?.clearFile();
            }}
            ossScene={UploadScene.CropImage}
            placeholder="点击或者拖拽图片到该区域，可批量上传，支持 PNG"
            ref={uploaderRef}
            uploadType="oss"
            useOriginName
          />
        </div>
        <NScrollbar
          class="flex-1 flex-shrink-0"
          ref={scrollbarRef}
        >
          <div class="h-full transform-translate-z-0" ref={containerRef}>
            <NCollapse
              accordion
              arrow-placement="right"
              display-directive="show"
              expanded-names={currentExpanded.value}
              onUpdate:expandedNames={onExpandedNamesChange}
            >
              {{
                arrow: () => <> </>,
                default: () => (
                  <>
                    {props.list.map((task) => (
                      <NCollapseItem class="mb-16px" key={task.id} name={task.id}>
                        {{
                          'header': () => (
                            <div class="flex-c-between w-full gap-8px">
                              <div class="c-text-gray1 FO-Font-B14 flex-c-start gap-4px">
                                {[CuttingTaskStatus.FINISHED, CuttingTaskStatus.PROCESSING].includes(task.status) && (
                                  <NIcon class="c-FO-Brand-Primary-Default" size={20}>
                                    <BasicFillAISlice />
                                  </NIcon>
                                )}
                                {task.status === CuttingTaskStatus.CANCELED && (
                                  <NIcon size={20}>
                                    <SystemFillClose />
                                  </NIcon>
                                )}
                                {task.title}
                                {[CuttingTaskStatus.CANCELED, CuttingTaskStatus.PROCESSING].includes(task.status) && (
                                  <div class="rd-4px bg-FO-Container-Fill2 px-12px"> {
                                    task.safeImages?.filter((item) => [ImageHandlingState.COMPLETED, ImageHandlingState.EDITED, ImageHandlingState.FINISHED].includes(item.state)).length || 0
                                  }/{task.images.length}
                                  </div>
                                )}
                                {task.status === CuttingTaskStatus.PENDING && (
                                  <div class="rd-4px bg-FO-Container-Fill2 px-12px">{task.images.length}</div>
                                )}
                                {task.status === CuttingTaskStatus.FINISHED
                                  ? (
                                    <div class="rd-4px bg-FO-Container-Fill2 px-12px">{task.images.length}</div>
                                  )
                                  : null}
                              </div>
                              <div class="mr-8px">
                                {renderActions(task)}
                              </div>
                            </div>
                          ),
                          'header-extra': () => (
                            <div class={
                              [
                                'h-32px w-32px flex items-center justify-center rd-6px bg-FO-Container-Fill2',
                                props.list.length > 1 ? 'cursor-pointer' : 'cursor-not-allowed',
                              ]
                            }
                            >
                              <NIcon size={14}>
                                <ArrowForwardIosRound class={[currentExpanded.value[0] === task.id ? 'rotate-90' : '', 'transition-all']} />
                              </NIcon>
                            </div>
                          ),
                          'default': () => (
                            <TaskItem
                              activeKey={props.activeImageKey}
                              isDrawing={props.isDrawing}
                              key={task.id}
                              onActivated={props.onUpdateActiveImageKey}
                              onRemoveImage={onRemoveImage}
                              onScrollToActive={onScrollToActiveImage}
                              onTaskTypeChange={props.onUpdateTaskType}
                              onUpdateFiles={(file, index) => {
                                const targetTaskIndex = props.list.findIndex((t) => t.id === props.currentKey);
                                if (targetTaskIndex !== -1) {
                                  const updatedImages = task.images.map((img, idx) => {
                                    if (idx === index) {
                                      return {
                                        ...img,
                                        basicImage: file,
                                        name: file.name, // 更新唯一名
                                      };
                                    }
                                    return img;
                                  });
                                  props.onUpdateImages(targetTaskIndex, updatedImages);
                                }
                              }}
                              task={task}
                              taskTypeList={props.taskTypeList}
                            />
                          ),
                        }}
                      </NCollapseItem>
                    ))}
                  </>
                ),
              }}

            </NCollapse>
          </div>
        </NScrollbar>

        <div class="mt-24px">
          <NButton
            class="btn-fill-primary w-full"
            disabled={submitDisabled.value}
            loading={submitLoading.value}
            onClick={() => {
              props.onSubmit(props.currentKey);
            }}
            renderIcon={() => <BasicFillAISlice />}
          >
            {submitLoading.value ? '自动切图中' : '一键处理'}
          </NButton>
        </div>
      </div>
    );
  },
});

export {
  CuttingTaskSide,
};
