import Icon, { CloseCircleFilled } from '@ant-design/icons-vue';
import { Button, Col, Input, RangePicker, Row, Select } from 'ant-design-vue';
import { type PropType, computed, defineComponent, ref } from 'vue';
import dayjs, { type Dayjs } from 'dayjs';
import { ForgeonFilterPopover, ForgeonUserSelector } from '@hg-tech/oasis-common';
import { useUserListOption } from '../../composables/useUserSearch';
import type { MergeServiceGetMergeRecordParams } from '@hg-tech/api-schema-merge';
import { MergeV1FailReasonLabelMap, MergeV1FailReasonOptions, renderMergeV1FailReasonIcon } from '../../models/config.model';
import { useUserAuthStore } from '../../store/modules/userAuth';
import { store } from '../../store/pinia';
import BasicFillFilter from '../../assets/svg/BasicFillFilter.svg?component';

const FilterWrapper = defineComponent({
  props: {
    form: {
      type: Object as PropType<MergeServiceGetMergeRecordParams>,
      default: () => ({}),
    },
  },
  emits: ['update:form'],
  setup(props, { emit }) {
    const form = computed({
      get: () => props.form,
      set: (value) => emit('update:form', value),
    });

    const { userProfile } = useUserAuthStore(store);
    const { userListOptions, userListLoading, getUserItemFromSession, queryUser, resetUserList } = useUserListOption(computed(() => [{
      hgAccount: userProfile?.userName || '',
      name: userProfile?.nickName || '',
      nickName: userProfile?.moniker || '',
      openId: userProfile?.openID || '',
      avatar: userProfile?.headerImg || '',
    }]));

    const rangePresets = ref([
      { label: '今天', value: [dayjs().startOf('day'), dayjs().endOf('day')] },
      { label: '昨天', value: [dayjs().add(-1, 'd').startOf('day'), dayjs().add(-1, 'd').endOf('day')] },
      { label: '最近3天', value: [dayjs().add(-3, 'd'), dayjs()] },
      { label: '最近7天', value: [dayjs().add(-7, 'd'), dayjs()] },
      { label: '最近14天', value: [dayjs().add(-14, 'd'), dayjs()] },
      { label: '最近30天', value: [dayjs().add(-30, 'd'), dayjs()] },
    ]);

    const renderUserItem = (item: string) => {
      const targetUser = getUserItemFromSession(item);
      return targetUser ? targetUser.name || targetUser.nickName : '未知用户';
    };

    const renderDate = (timestamp?: string) => {
      return timestamp ? dayjs(Number(timestamp) * 1000).format('YYYY-MM-DD HH:mm:ss') : '';
    };

    const renderClear = (visible: boolean, clear: () => void) => {
      return visible && (
        <Icon
          class="ml-4px cursor-pointer px-4px"
          component={CloseCircleFilled}
          onClick={(e) => {
            e.stopPropagation();
            clear();
          }}
        />
      );
    };

    const filterConfigs = computed(() => [
      {
        key: 'cl',
        label: 'CL号',
        value: [form.value.clStart, form.value.clEnd],
        tooltip: Boolean(form.value.clStart || form.value.clEnd),
        tooltipContent: `CL号：${form.value.clStart || '任意'} - ${form.value.clEnd || '任意'}`,
        isActive: Boolean(form.value.clStart || form.value.clEnd),
        iconClass: (form.value.clStart || form.value.clEnd ? 'c-FO-Brand-Primary-Default' : 'c-FO-Content-Icon2'),
        buttonClass: (form.value.clStart || form.value.clEnd ? 'btn-fill-secondary' : 'btn-fill-default'),
        clear: () => {
          form.value.clStart = undefined;
          form.value.clEnd = undefined;
        },
        content: () => (
          <Input.Group>
            <Row align="middle" gutter={8} justify="center">
              <Col>
                <Input allowClear placeholder="请输入CL开始号" v-model:value={form.value.clStart} />
              </Col>
              <Col>
                至
              </Col>
              <Col>
                <Input allowClear placeholder="请输入CL结束号" v-model:value={form.value.clEnd} />
              </Col>
            </Row>
          </Input.Group>
        ),
        buttonText: () => (
          <>
            <span>CL号</span>
            {form.value.clStart || form.value.clEnd ? <span>：{form.value.clStart} - {form.value.clEnd}</span> : null}
          </>
        ),
      },
      {
        key: 'submitTime',
        label: '提交时间',
        value: [form.value.submitTimeStart, form.value.submitTimeEnd],
        tooltip: Boolean(form.value.submitTimeStart || form.value.submitTimeEnd),
        tooltipContent: `提交时间：${renderDate(form.value.submitTimeStart)} - ${renderDate(form.value.submitTimeEnd)}`,
        isActive: Boolean(form.value.submitTimeStart || form.value.submitTimeEnd),
        iconClass: (form.value.submitTimeStart || form.value.submitTimeEnd ? 'c-FO-Brand-Primary-Default' : 'c-FO-Content-Icon2'),
        buttonClass: (form.value.submitTimeStart || form.value.submitTimeEnd ? 'btn-fill-secondary' : 'btn-fill-default'),
        clear: () => {
          form.value.submitTimeStart = undefined;
          form.value.submitTimeEnd = undefined;
        },
        content: () => {
          const submitTime = computed({
            get: () => [
              form.value.submitTimeStart ? dayjs(Number(form.value.submitTimeStart) * 1000) : undefined,
              form.value.submitTimeEnd ? dayjs(Number(form.value.submitTimeEnd) * 1000) : undefined,
            ] as [Dayjs | undefined, Dayjs | undefined],
            set: (value: [Dayjs | undefined, Dayjs | undefined]) => {
              form.value.submitTimeStart = value?.[0]
                ? Math.floor(value[0]?.valueOf() / 1000).toString()
                : undefined;
              form.value.submitTimeEnd = value?.[1]
                ? Math.floor(value[1]?.valueOf() / 1000).toString()
                : undefined;
            },
          });
          return (
            <RangePicker presets={rangePresets.value} show-time v-model:value={submitTime.value} />
          );
        },
        buttonText: () => (
          <>
            <span>提交时间</span>
            {
              form.value.submitTimeStart || form.value.submitTimeEnd
                ? <span>：{renderDate(form.value.submitTimeStart)} - {renderDate(form.value.submitTimeEnd)}</span>
                : null
            }
          </>
        ),
      },
      {
        key: 'handler',
        label: '处理人',
        value: form.value.handler,
        tooltip: Boolean(form.value.handler && form.value.handler.length),
        tooltipContent: `处理人：${form.value.handler && form.value.handler.length ? form.value.handler.map(renderUserItem).join(', ') : ''}`,
        isActive: Boolean(form.value.handler && form.value.handler.length),
        iconClass: (form.value.handler && form.value.handler.length ? 'c-FO-Brand-Primary-Default' : 'c-FO-Content-Icon2'),
        buttonClass: (form.value.handler && form.value.handler.length ? 'btn-fill-secondary' : 'btn-fill-default'),
        clear: () => {
          form.value.handler = [];
        },
        content: () => (
          <ForgeonUserSelector
            class="w-300px"
            loading={userListLoading.value}
            multiple
            onReset={resetUserList}
            onSearch={(params) => {
              queryUser(params, {});
            }}
            options={userListOptions.value}
            placeholder="请输入用户姓名/昵称/邮箱/拼音"
            showAvatar
            v-model:value={form.value.handler}
          />
        ),
        buttonText: () => (
          <>
            <span>处理人</span>
            {form.value.handler && form.value.handler.length
              ? form.value.handler.length > 1
                ? <span>：{renderUserItem(form.value.handler[0])}等{form.value.handler.length}人</span>
                : <span>：{renderUserItem(form.value.handler[0])}</span>
              : null}
          </>
        ),
      },
      {
        key: 'failReason',
        label: '失败原因',
        value: form.value.failReason,
        tooltip: Boolean(form.value.failReason && form.value.failReason.length),
        tooltipContent: `失败原因：${form.value.failReason && form.value.failReason.length ? form.value.failReason.map((i) => MergeV1FailReasonLabelMap[i]).join(', ') : ''}`,
        isActive: Boolean(form.value.failReason && form.value.failReason.length),
        iconClass: (form.value.failReason && form.value.failReason.length ? 'c-FO-Brand-Primary-Default' : 'c-FO-Content-Text2'),
        buttonClass: (form.value.failReason && form.value.failReason.length ? 'btn-fill-secondary' : 'btn-fill-default'),
        clear: () => {
          form.value.failReason = [];
        },
        content: () => (
          <Select
            allowClear
            class="w-300px"
            mode="multiple"
            placeholder="请选择失败原因"
            showSearch={false}
            v-model:value={form.value.failReason}
          >
            {MergeV1FailReasonOptions.map((option) => (
              <Select.Option key={option.value} label={option.label} value={option.value}>
                <span class="flex items-center gap-8px">
                  {renderMergeV1FailReasonIcon[option.value]()}
                  {option.label}
                </span>
              </Select.Option>
            ))}
          </Select>
        ),
        buttonText: () => (
          <>
            <span>失败原因</span>
            {form.value.failReason && form.value.failReason.length
              ? form.value.failReason.length > 1
                ? <span>：{MergeV1FailReasonLabelMap[form.value.failReason?.[0]]}等{form.value.failReason?.length}项</span>
                : <span>：{MergeV1FailReasonLabelMap[form.value.failReason?.[0]]}</span>
              : null}
          </>
        ),
      },
    ]);

    return () => (
      <div class="filter-wrapper flex flex-wrap items-center gap-10px">
        {filterConfigs.value.map((config) => (
          <ForgeonFilterPopover
            key={config.key}
            placement="bottomLeft"
            tooltip={config.tooltip}
            tooltipContent={config.tooltipContent}
            trigger="click"
          >
            {{
              default: () => (
                <Button
                  class={[
                    config.buttonClass,
                    'flex items-center justify-between gap-4px',
                  ]}
                  icon={<Icon class={config.iconClass} component={BasicFillFilter} />}
                >
                  <span class={config.isActive ? 'c-FO-Brand-Primary-Default' : 'c-FO-Content-Text2'}>
                    {config.buttonText()}
                    {renderClear(config.isActive, config.clear)}
                  </span>
                </Button>
              ),
              content: config.content,
            }}
          </ForgeonFilterPopover>
        ))}
      </div>
    );
  },
});

export {
  FilterWrapper,
};
