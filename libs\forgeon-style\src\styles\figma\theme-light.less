/* This file is automatically generated. DO NOT EDIT it manually. */

/** map theme vars to basic var for light.  */
:root[data-theme='light'] {
  --FO-Brand-Primary-Default: var(--Light-Violet-6);
  --FO-Brand-Primary-Hover: var(--Light-Violet-5);
  --FO-Brand-Primary-Active: var(--Light-Violet-7);
  --FO-Brand-Primary-Disabled: var(--Light-Violet-3);
  --FO-Brand-Secondary-Default: var(--Light-Violet-1);
  --FO-Brand-Secondary-Hover: var(--Light-Violet-2);
  --FO-Brand-Secondary-Active: var(--Light-Violet-3);
  --FO-Brand-Secondary-Disabled: var(--Light-Violet-1);
  --FO-Brand-Tertiary-Active: var(--Light-Violet-1);
  --FO-Functional-Success1-Default: var(--Light-Green-6);
  --FO-Functional-Success1-Hover: var(--Light-Green-5);
  --FO-Functional-Success1-Active: var(--Light-Green-7);
  --FO-Functional-Success1-Disabled: var(--Light-Green-3);
  --FO-Functional-Success2-Default: var(--Light-Green-1);
  --FO-Functional-Success2-Hover: var(--Light-Green-2);
  --FO-Functional-Success2-Active: var(--Light-Green-3);
  --FO-Functional-Success2-Disabled: var(--Light-Green-1);
  --FO-Functional-Warning1-Default: var(--Light-Orange-6);
  --FO-Functional-Warning1-Hover: var(--Light-Orange-5);
  --FO-Functional-Warning1-Active: var(--Light-Orange-7);
  --FO-Functional-Warning1-Disabled: var(--Light-Orange-3);
  --FO-Functional-Warning2-Default: var(--Light-Orange-1);
  --FO-Functional-Warning2-Hover: var(--Light-Orange-2);
  --FO-Functional-Warning2-Active: var(--Light-Orange-3);
  --FO-Functional-Warning2-Disabled: var(--Light-Orange-1);
  --FO-Functional-Error1-Default: var(--Light-Red-6);
  --FO-Functional-Error1-Hover: var(--Light-Red-5);
  --FO-Functional-Error1-Active: var(--Light-Red-7);
  --FO-Functional-Error1-Disabled: var(--Light-Red-3);
  --FO-Functional-Error2-Default: var(--Light-Red-1);
  --FO-Functional-Error2-Hover: var(--Light-Red-2);
  --FO-Functional-Error2-Active: var(--Light-Red-3);
  --FO-Functional-Error2-Disabled: var(--Light-Red-1);
  --FO-Functional-Info1-Default: var(--Light-Blue-6);
  --FO-Functional-Info1-Hover: var(--Light-Blue-5);
  --FO-Functional-Info1-Active: var(--Light-Blue-7);
  --FO-Functional-Info1-Disabled: var(--Light-Blue-3);
  --FO-Functional-Info2-Default: var(--Light-Blue-1);
  --FO-Functional-Info2-Hover: var(--Light-Blue-2);
  --FO-Functional-Info2-Active: var(--Light-Blue-3);
  --FO-Functional-Info2-Disabled: var(--Light-Blue-1);
  --FO-Content-Text0: var(--Basic-White-100);
  --FO-Content-Text1: var(--Light-Gray-15);
  --FO-Content-Text2: var(--Light-Gray-11);
  --FO-Content-Text3: var(--Light-Gray-8);
  --FO-Content-Text4: var(--Light-Gray-6);
  --FO-Content-Icon0: var(--Basic-White-100);
  --FO-Content-Icon1: var(--Light-Gray-14);
  --FO-Content-Icon2: var(--Light-Gray-10);
  --FO-Content-Icon3: var(--Light-Gray-7);
  --FO-Content-Icon4: var(--Light-Gray-5);
  --FO-Content-Components1: var(--Light-Gray-0);
  --FO-Content-Components2: var(--Light-Gray-0);
  --FO-Content-Link-Default: var(--Light-Blue-6);
  --FO-Content-Link-Hover: var(--Light-Blue-5);
  --FO-Content-Link-Active: var(--Light-Blue-7);
  --FO-Content-Link-Disabled: var(--Light-Blue-3);
  --FO-Container-Mask40b: var(--Basic-Black-40);
  --FO-Container-Mask60w: var(--Basic-White-60);
  --FO-Container-Background: var(--Light-Gray-1);
  --FO-Container-Fill0: var(--Basic-White-0);
  --FO-Container-Fill1: var(--Light-Gray-0);
  --FO-Container-Fill2: var(--Light-Gray-1);
  --FO-Container-Fill3: var(--Light-Gray-2);
  --FO-Container-Fill4: var(--Light-Gray-3);
  --FO-Container-Fill5: var(--Light-Gray-4);
  --FO-Container-Fill6: var(--Dark-Gray-1);
  --FO-Container-Stroke0: var(--Basic-White-100);
  --FO-Container-Stroke1: var(--Light-Gray-3);
  --FO-Container-Stroke2: var(--Light-Gray-4);
  --FO-Container-Stroke3: var(--Light-Gray-5);
  --FO-Container-Stroke4: var(--Light-Gray-6);
  --FO-Datavis-Violet1: var(--Light-Violet-6);
  --FO-Datavis-Violet2: var(--Light-Violet-4);
  --FO-Datavis-Violet3: var(--Light-Violet-1);
  --FO-Datavis-Blue1: var(--Light-Blue-6);
  --FO-Datavis-Blue2: var(--Light-Blue-4);
  --FO-Datavis-Blue3: var(--Light-Blue-1);
  --FO-Datavis-Lightblue1: var(--Light-Lightblue-7);
  --FO-Datavis-Lightblue2: var(--Light-Lightblue-5);
  --FO-Datavis-Lightblue3: var(--Light-Lightblue-1);
  --FO-Datavis-Teal1: var(--Light-Teal-7);
  --FO-Datavis-Teal2: var(--Light-Teal-5);
  --FO-Datavis-Teal3: var(--Light-Teal-1);
  --FO-Datavis-Green1: var(--Light-Green-7);
  --FO-Datavis-Green2: var(--Light-Green-4);
  --FO-Datavis-Green3: var(--Light-Green-1);
  --FO-Datavis-Lightgreen1: var(--Light-Lightgreen-7);
  --FO-Datavis-Lightgreen2: var(--Light-Lightgreen-4);
  --FO-Datavis-Lightgreen3: var(--Light-Lightgreen-1);
  --FO-Datavis-Yellow1: var(--Light-Yellow-8);
  --FO-Datavis-Yellow2: var(--Light-Yellow-7);
  --FO-Datavis-Yellow3: var(--Light-Yellow-1);
  --FO-Datavis-Orange1: var(--Light-Orange-7);
  --FO-Datavis-Orange2: var(--Light-Orange-5);
  --FO-Datavis-Orange3: var(--Light-Orange-1);
  --FO-Datavis-Red1: var(--Light-Red-6);
  --FO-Datavis-Red2: var(--Light-Red-4);
  --FO-Datavis-Red3: var(--Light-Red-1);
  --FO-Datavis-Pink1: var(--Light-Pink-6);
  --FO-Datavis-Pink2: var(--Light-Pink-4);
  --FO-Datavis-Pink3: var(--Light-Pink-1);
  --FO-Datavis-Purple1: var(--Light-Purple-6);
  --FO-Datavis-Purple2: var(--Light-Purple-4);
  --FO-Datavis-Purple3: var(--Light-Purple-1);
}
