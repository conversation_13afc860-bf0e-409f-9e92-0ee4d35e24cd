<template>
  <div class="app-content h-full flex flex-col overflow-hidden">
    <ARadio.Group v-model:value="appActiveKey" class="app-tabs" @change="() => handleAppActiveKeyChange()">
      <ARadioButton value="uploaded">
        已上传
      </ARadioButton>
      <ARadioButton value="installed">
        已安装
      </ARadioButton>
    </ARadio.Group>

    <div class="app-container h-full flex-1 overflow-hidden p-4">
      <!-- 已上传内容 -->
      <div v-if="appActiveKey === 'uploaded'" class="h-full flex flex-1 flex-col overflow-hidden">
        <AUpload.Dragger
          v-model:fileList="websocketStore.fileList" name="file" :maxCount="1"
          :accept="deviceStore.isAndroid ? '.apk' : '.ipa'" :beforeUpload="beforeUpload"
          :customRequest="handleUploadFile" class="upload-dragger" :disabled="websocketStore.loading || isUploading"
          :showUploadList="false" @change="handleChange" @reject="() => handleReject()"
        >
          <div class="flex flex-col items-center justify-center">
            <Icon :icon="UploadTwo" :size="48" class="c-FO-Content-Text2 opacity-30" />
            <div class="mt-2 c-FO-Content-Text2">
              将安装包拖拽到此处，或点击下方按钮（支持 {{ deviceStore.isAndroid ? '.apk' : '.ipa' }} 格式）
            </div>
            <div class="mt-4 flex items-center justify-center gap-2">
              <AButton
                type="primary" :disabled="websocketStore.loading || isUploading"
                @click.stop="handlePackageCenterUpload"
              >
                包体中心上传
              </AButton>
              <AButton :disabled="websocketStore.loading || isUploading">
                本地上传
              </AButton>
            </div>
          </div>
        </AUpload.Dragger>
        <div
          v-if="websocketStore.fileList?.length"
          v-tippy="websocketStore.fileList?.[0]?.status === 'error' ? websocketStore.fileList?.[0]?.response : ''"
          class="group mt-2"
          :class="{ 'c-FO-Functional-Error1-Default': websocketStore.fileList?.[0]?.status === 'error' }"
        >
          <div class="h-32px flex items-center justify-between rounded-4px px-4 group-hover:bg-FO-Container-Fill2">
            <div class="min-w-0 flex flex-1 items-center gap-2">
              <Icon :icon="LinkIcon" />
              <EllipsisText class="text-xs">
                {{ websocketStore.fileList?.[0]?.name }}
              </EllipsisText>
            </div>
            <APopconfirm
              :title="`确定${isUploading ? '终止' : '删除'}该上传吗？`" placement="left"
              @confirm="() => handleDeleteFile()"
            >
              <ATooltip :title="isUploading ? '终止上传' : '删除上传'">
                <AButton
                  type="text" danger class="px-6px opacity-0 group-hover:opacity-100"
                  :class="{ 'opacity-100': websocketStore.fileList?.[0]?.status === 'error' }"
                >
                  <Icon :icon="DeleteIcon" />
                </AButton>
              </ATooltip>
            </APopconfirm>
          </div>
          <AProgress v-if="isUploading" :percent="showPercent" :size="2" />
        </div>
        <div class="recent-uploads mt-6 h-full flex flex-1 flex-col overflow-hidden">
          <div class="section-title mb-2">
            最近上传的应用
          </div>
          <div v-if="!websocketStore.recentApps?.length" class="py-4 text-center c-FO-Content-Text2">
            <AEmpty description="暂无上传记录" />
          </div>
          <div v-else class="app-list h-full flex flex-auto flex-col gap-2 overflow-y-auto">
            <div
              v-for="app in websocketStore.recentApps" :key="app.id"
              class="app-item flex items-center rounded-md bg-FO-Container-Fill2 p-4"
            >
              <div class="app-icon mr-3 h-10 w-10 flex items-center justify-center rounded-md">
                <img v-if="app.icon" :src="app.icon" class="h-full w-full rounded-md">
                <Icon v-else :icon="AppIcon" :size="24" />
              </div>
              <div class="app-info min-w-0 flex-1">
                <EllipsisText class="app-name font-bold">
                  {{ app.appName || app.pkgName }}
                </EllipsisText>
                <div>
                  <EllipsisText class="app-meta text-xs c-FO-Content-Text2">
                    {{ app.version }} | {{ app.size }} | {{ app.createTime }}
                  </EllipsisText>
                </div>
              </div>
              <APopconfirm title="确定删除该上传记录吗？" :disabled="app.isInstalling" @confirm="handleDeleteApp(app)">
                <AButton class="mr-2" :disabled="app.isInstalling">
                  删除
                </AButton>
              </APopconfirm>
              <AButton
                v-if="!app.isInstalling && app.isInstalled" type="primary" :disabled="websocketStore.loading"
                @click="openApp(app.pkgName)"
              >
                打开
              </AButton>
              <AButton
                v-else :type="app.isInstalled ? 'primary' : 'default'" :loading="app.isInstalling"
                class="relative flex items-center overflow-hidden transition-300"
                :class="{ '!opacity-100': app.isInstalling && websocketStore.installStatus }" :style="app.isInstalling && websocketStore.installPercent >= 0 ? {
                  background: `linear-gradient(to right, ${ForgeonThemeCssVar.BrandSecondaryHover} ${websocketStore.installPercent}%, ${ForgeonThemeCssVar.ContentIcon0} ${websocketStore.installPercent}%)`,
                } : {}" :disabled="websocketStore.loading || hasOtherUploading(app)" @click="handleInstall(app)"
              >
                <div class="flex items-center gap-2">
                  <span :class="{ 'c-FO-Content-Text2': app.isInstalling }">
                    {{ app.isInstalling && websocketStore.installStatus ? showInstallStatusName : '安装' }}
                  </span>
                  <template v-if="app.isInstalling && websocketStore.installStatus">
                    <span class="text-xs c-FO-Content-Text2">
                      {{ websocketStore.installPercent }}%
                    </span>
                    <div class="h-12px w-1px bg-FO-Content-Text4" />
                    <APopconfirm
                      title="确认终止安装吗？" placement="left"
                      :disabled="websocketStore.installStatus === 'installing'" @confirm="() => handleCancelInstall()"
                    >
                      <ATooltip :title="websocketStore.installStatus !== 'installing' ? '终止安装' : '安装过程中无法终止'">
                        <StopIcon
                          class="size-16px outline-none"
                          :class="websocketStore.installStatus !== 'installing' ? 'cursor-pointer c-FO-Content-Text2 hover:c-FO-Content-Text1' : 'cursor-not-allowed  c-FO-Content-Text4'"
                        />
                      </ATooltip>
                    </APopconfirm>
                  </template>
                </div>
              </AButton>
            </div>
          </div>
        </div>
      </div>

      <!-- 已安装内容 -->
      <div v-if="appActiveKey === 'installed'" class="h-full flex flex-1 flex-col">
        <div class="mb-4 flex items-center justify-between">
          <div class="flex items-center gap-4">
            <span>共 {{ websocketStore.appList.length }} 个应用</span>
            <AButton
              class="flex items-center px-3" :disabled="websocketStore.loading"
              @click="() => websocketStore.refreshAppList(true)"
            >
              <Icon :icon="RefreshIcon" />
              刷新
            </AButton>
          </div>
          <AInput
            v-model:value="keyword" placeholder="搜索应用名或包名" class="w-240px" :disabled="websocketStore.loading"
            @change="handleSearch"
          >
            <template #suffix>
              <Icon :icon="SearchIcon" class="c-FO-Content-Text2" />
            </template>
          </AInput>
        </div>

        <div v-if="showAppList.length" class="h-full flex flex-1 flex-col">
          <UseVirtualList
            ref="scrollRef" :key="windowHeight" :list="showAppList"
            :options="{ itemHeight: 80, overscan: 10 }" :height="`${windowHeight - 320}px`"
          >
            <template #default="{ data: app }">
              <div class="py-1">
                <div class="app-item flex items-center gap-3 rounded-md bg-FO-Container-Fill2 p-4">
                  <div class="app-icon h-10 w-10 flex items-center justify-center rounded-md">
                    <img
                      v-if="app.appIcon || app.iconBase64"
                      :src="`data:image/png;base64,${deviceStore.isAndroid ? app.appIcon : app.iconBase64}`"
                      class="h-full w-full rounded-md"
                    >
                    <Icon v-else :icon="AppIcon" :size="24" />
                  </div>
                  <div class="app-info min-w-0 flex-1">
                    <EllipsisText class="app-name font-bold">
                      {{ deviceStore.isAndroid ? app.appName : app.name }}
                    </EllipsisText>
                    <div>
                      <EllipsisText class="app-meta text-xs c-FO-Content-Text2">
                        {{ deviceStore.isAndroid ? app.packageName : app.bundleId }} | {{ deviceStore.isAndroid
                          ? app.versionName : app.shortVersion }}
                      </EllipsisText>
                    </div>
                  </div>
                  <div class="flex items-center gap-2">
                    <AButton
                      type="primary"
                      @click="() => openApp(deviceStore.isAndroid ? app.packageName : app.bundleId)"
                    >
                      打开
                    </AButton>
                    <AButton @click="() => killApp(deviceStore.isAndroid ? app.packageName : app.bundleId)">
                      停止
                    </AButton>
                    <APopconfirm
                      title="确定卸载该应用吗？"
                      @confirm="() => uninstallApp(deviceStore.isAndroid ? app.packageName : app.bundleId)"
                    >
                      <AButton>
                        卸载
                      </AButton>
                    </APopconfirm>
                  </div>
                </div>
              </div>
            </template>
          </UseVirtualList>
        </div>
        <div class="py-8 c-FO-Content-Text2">
          <AEmpty :description="!websocketStore.appList.length && !keyword ? '暂无应用' : '没有找到匹配的应用'" />
        </div>
      </div>
    </div>
    <PackageCenterModal @register="registerPackageCenterModal" @success="handlePackageCenterSuccess" />
  </div>
</template>

<script lang="ts" setup>
import { computed, onBeforeUnmount, ref } from 'vue';
import UploadTwo from '@iconify-icons/icon-park-outline/upload-two';
import AppIcon from '@iconify-icons/icon-park-outline/application';
import SearchIcon from '@iconify-icons/icon-park-outline/search';
import RefreshIcon from '@iconify-icons/icon-park-outline/refresh';
import LinkIcon from '@iconify-icons/icon-park-outline/link';
import DeleteIcon from '@iconify-icons/icon-park-outline/delete';
import { Button as AButton, Empty as AEmpty, Input as AInput, Popconfirm as APopconfirm, Progress as AProgress, Radio as ARadio, RadioButton as ARadioButton, Tooltip as ATooltip, Upload as AUpload, message, Modal } from 'ant-design-vue';
import { useCloudDeviceStore, useCloudDeviceWebsocketStore } from '../../stores';
import { Icon } from '/@/components/Icon';
import PackageCenterModal from './components/PackageCenterModal.vue';
import { useModal } from '/@/components/Modal';
import { useAppManagement } from '../../composables/useAppManagement';
import { EllipsisText } from '/@/components/EllipsisText';
import { UseVirtualList } from '@vueuse/components';
import { useWindowSize, watchOnce } from '@vueuse/core';
import { deleteMtlInstallRecord } from '/@/api/page/mtl/device';
import type { MtlInstallRecordListItem } from '/@/api/page/mtl/model/deviceModel';
import type { UploadRequestError, UploadRequestOption } from 'ant-design-vue/es/vc-upload/interface';
import { buildUUID } from '/@/utils/uuid';
import { useRoute } from 'vue-router';
import { getGamePackageByID, getGamePackagesVersionByID } from '/@/api/page/test';
import { useUserStoreWithOut } from '/@/store/modules/user';
import type { GamePackagesListItem, GamePackagesVersionsListItem } from '/@/api/page/model/testModel';
import { useCurrentProjectInfo } from '../../../../../hooks/useProjects.ts';
import { sendEvent } from '../../../../../service/tracker/index.ts';
import { useErrorHandler } from '../../composables/useErrorHandler.tsx';
import { ForgeonThemeCssVar } from '@hg-tech/forgeon-style';
import StopIcon from '../../../../../assets/icons/mtl/stop.svg';

const userStore = useUserStoreWithOut();
const route = useRoute();
const urlPkgID = Number(route.query.pkgId) || undefined;
const urlVersionID = Number(route.query.pkgVersionId) || undefined;
const pkgInfo = ref<GamePackagesListItem>();
const pkgVersionInfo = ref<GamePackagesVersionsListItem>();

const appActiveKey = ref<'uploaded' | 'installed'>('uploaded');
const deviceStore = useCloudDeviceStore();
const websocketStore = useCloudDeviceWebsocketStore();
const { selectPackage, uninstallApp, openApp, killApp, uploadFile, uploadLoading, install, cancelUpload, cancelInstall, handleInstall, handleCancelInstall } = useAppManagement();

const keyword = ref('');
const scrollRef = ref();
const isAppStoreInstall = ref(false);
const { height: windowHeight } = useWindowSize();
const { projectInfo } = useCurrentProjectInfo();
const { showErrorWithFault } = useErrorHandler();

const isUploading = computed(() => {
  return uploadLoading.value || !!websocketStore.curUploadingUUID;
});

function handleAppActiveKeyChange() {
  if (appActiveKey.value === 'installed') {
    sendEvent('cloud_device_installed_app_click');
  }
}

/** 获取包信息 */
async function getPkgInfo() {
  if (!userStore.getProjectId || !urlPkgID) {
    return;
  }
  const { regamePkg } = await getGamePackageByID(
    userStore.getProjectId,
    urlPkgID,
    'none',
  );
  pkgInfo.value = regamePkg;
}

/** 获取包版本信息 */
async function getPkgVersionInfo() {
  if (!userStore.getProjectId || !urlPkgID || !urlVersionID) {
    return;
  }
  const { repkgVersion } = await getGamePackagesVersionByID(
    userStore.getProjectId,
    urlPkgID,
    urlVersionID,
    'none',
  );
  pkgVersionInfo.value = repkgVersion;
}

const showAppList = computed(() => {
  return websocketStore.appList.filter((app) => {
    const lowerKeyword = keyword.value?.toLowerCase()?.trim();
    if (deviceStore.isAndroid) {
      return !lowerKeyword || app.appName?.toLowerCase()?.includes(lowerKeyword) || app.packageName?.toLowerCase()?.includes(lowerKeyword);
    } else {
      return !lowerKeyword || app.name?.toLowerCase()?.includes(lowerKeyword) || app.bundleId?.toLowerCase()?.includes(lowerKeyword);
    }
  });
});

const showUploadPercent = computed(() => {
  return Math.round((websocketStore.fileList[0]?.percent || 0) / 2);
});

const showDownloadPercent = computed(() => {
  return isAppStoreInstall.value ? websocketStore.downloadPercent * 2 : websocketStore.downloadPercent;
});

const showPercent = computed(() => {
  return showUploadPercent.value + showDownloadPercent.value;
});

const showInstallStatusName = computed(() => {
  switch (websocketStore.installStatus) {
    case 'downloading':
      return '下载中';
    case 'installing':
      return '安装中';
    case 'resourcing':
      return '推送资源中';
    default:
      return '';
  }
});

const [registerPackageCenterModal, { openModal: openPackageCenterModal }] = useModal();

function handlePackageCenterUpload() {
  openPackageCenterModal(true, {});
}

/**
 * 判断是否存在其他安装中的应用
 * @param app 应用信息
 * @returns 是否存在其他安装中的应用
 */
function hasOtherUploading(app: MtlInstallRecordListItem) {
  return websocketStore.recentApps.some((file) => file.uuid !== app.uuid && file.isInstalling);
}

function handlePackageCenterSuccess(data: any, isPkgTest = false) {
  isAppStoreInstall.value = true;
  websocketStore.fileList = [];
  if (!isPkgTest) {
    websocketStore.fileList = [
      {
        uid: '1',
        name: data.name,
        size: 1,
        status: 'done',
        url: '',
      },
    ];
  }
  message.success('开始上传，请稍后...');
  selectPackage({
    val: data,
    curUuid: buildUUID(),
    area: 'upload',
    isPkgTest,
  });
}

function beforeUpload(file: File) {
  const isValid = deviceStore.isAndroid
    ? file.name.endsWith('.apk')
    : file.name.endsWith('.ipa');

  if (!isValid) {
    message.error('文件格式错误');
    websocketStore.fileList = [];
    return false;
  }
  if (file.size > 1024 * 1024 * 1024 * 2) {
    message.error('文件大小超过2GB');
    websocketStore.fileList = [];
    return false;
  }
  return true;
}

function handleReject() {
  message.error('文件格式错误');
}

function sendErrorUploadEvent() {
  sendEvent('cloud_device_app_upload', {
    is_success: false,
    time_length: 0,
    cloud_device_upload_type: '本地上传',
    cloud_device_package_size: websocketStore.fileList[0]?.size ? Number((websocketStore.fileList[0].size / 1024 / 1024).toFixed(1)) : 0,
    ...deviceStore.deviceTrackInfo,
  });
}

async function handleUploadFile(option: UploadRequestOption) {
  isAppStoreInstall.value = false;
  const { onProgress, onSuccess, onError } = option;

  try {
    const customOnProgress = (event: any) => {
      if (event.percent) {
        onProgress?.({ percent: event.percent });
      }
    };

    message.success('开始上传，请稍后...');
    const res = await uploadFile({
      ...option,
      onProgress: customOnProgress,
    });

    onSuccess?.(res);
    if (res.success && res.url) {
      install({
        val: { apk: deviceStore.isAndroid ? res.url : undefined, ipa: !deviceStore.isAndroid ? res.url : undefined },
        curUuid: buildUUID(),
        area: 'upload',
      });
    } else {
      sendErrorUploadEvent();
      if (res.status === 'removed') {
        websocketStore.fileList = [];
      } else {
        showErrorWithFault({ title: '上传失败', content: res.response });
        websocketStore.fileList[0] = {
          ...websocketStore.fileList[0],
          ...res,
        };
      }
    }
  } catch (error) {
    sendErrorUploadEvent();
    showErrorWithFault({ title: '上传失败', content: (error as any).message || '上传失败' });
    websocketStore.fileList[0] = {
      ...websocketStore.fileList[0],
      status: 'error',
      error: error as UploadRequestError,
    };
    onError?.(error as UploadRequestError);
  }
}

function handleDeleteFile() {
  if (websocketStore.fileList[0]?.status === 'uploading') {
    cancelUpload();
  } else if (websocketStore.curUploadingUUID) {
    cancelInstall(websocketStore.curUploadingUUID);
    websocketStore.curUploadingUUID = undefined;
  }
  message.info('上传已取消');
  websocketStore.fileList = [];
}

function handleChange(info: any) {
  if (info.file.status === 'uploading') {
    uploadLoading.value = true;
  } else if (info.file.status === 'done') {
    uploadLoading.value = false;
  } else if (info.file.status === 'error') {
    uploadLoading.value = false;
  }
}

function handleSearch() {
  scrollRef.value?.scrollTo(0);
}

async function handleDeleteApp(app: MtlInstallRecordListItem) {
  const { data: { code, message: msg } } = await deleteMtlInstallRecord({ id: app.id }, {});
  if (code === 2000) {
    message.success('删除成功');
    websocketStore.getRecentApps();
  } else {
    message.error(msg);
  }
}

// 当首次加载成功后，获取包信息和包版本信息并触发安装
watchOnce(() => websocketStore.loading, async (loading) => {
  if (!loading && urlPkgID && urlVersionID) {
    await getPkgInfo();
    await getPkgVersionInfo();
    if (pkgInfo.value && pkgVersionInfo.value) {
      handlePackageCenterSuccess({
        projectID: userStore.getProjectId,
        projectAlias: projectInfo.value?.alias,
        packageID: urlPkgID,
        versionID: urlVersionID,
        name: pkgInfo.value?.name,
      }, true);
    } else {
      Modal.warning({
        title: '获取包体失败，请重新选择',
        okText: '知道了',
      });
    }
  }
});

onBeforeUnmount(() => {
  cancelUpload();
});
</script>
