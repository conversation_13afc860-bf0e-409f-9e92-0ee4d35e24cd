<template>
  <div class="commit-center">
    <div v-for="depot in depotsList" :key="depot.ID" class="commit-center-card">
      <div class="flex items-center">
        <TypographyText :ellipsis="{ tooltip: true }" :content="depot.name || depot.prefix" class="FO-Font-B16 mb-4 max-w-300px!" />
      </div>
      <StreamsList :deptPrefix="depot.prefix" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { TypographyText } from 'ant-design-vue';
import { onMounted, ref } from 'vue';
import { type DepotsListItem, getDepotsListByPage } from '../../api';
import StreamsList from './StreamsList.vue';

const depotsList = ref<DepotsListItem[]>();
onMounted(async () => {
  const res = await getDepotsListByPage({ projectID: 1, page: 1, pageSize: 999 }, { });
  depotsList.value = res.data?.data?.list;
});
</script>

<style lang="less" scoped>
@import (reference) '@hg-tech/forgeon-style/vars.less';

.commit-center {
  &-card {
    margin-bottom: 16px;
    padding: 16px;
    border-radius: 8px;
    background-color: @FO-Container-Fill1;
    margin: 20px;

    &-list {
      &-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        padding: 16px;
        border: 1px solid @FO-Container-Stroke1;
        border-radius: 8px;
        background-color: @FO-Container-Fill1;

        &:hover {
          border-color: @FO-Brand-Primary-Default;
        }

        &:not(:last-child) {
          margin-bottom: 8px;
        }
      }
    }
  }
}
</style>
