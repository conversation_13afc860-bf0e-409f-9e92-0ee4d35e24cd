<template>
  <div v-track:v="'ihr6wkliv8'" class="p-4-depots">
    <ForgeonHeader
      class="p-4-depots-header" title="提交配置"
      :onHandleMenuExpand="onHandleExpand"
      :showUnfoldIcon="getShowMenuUnfoldBtn"
    >
      <template #actions>
        <Button class="custom-rounded-btn" @click="handleToManager">
          <div class="flex items-center">
            <SettingConfig class="mr-1 flex" theme="outline" />
            <span>项目配置</span>
          </div>
        </Button>
        <AppProjectSelect
          v-if="hasToken && getShowProjectSelect"
          :class="`${prefixCls}-project-select`"
          :theme="getDarkMode"
        />
        <AppHelpDoc v-if="getShowDoc" />
      </template>
    </ForgeonHeader>
    <div v-if="depotList?.length" :class="`${prefixCls}__body`">
      <transition-group name="flip-list" tag="p">
        <div v-for="(depot, index) in depotList" :key="depot.ID" :class="`${prefixCls}__card`">
          <P4Stream :depotID="depot.ID!" :isLocalDepot="depot.isLocal" :serverID="depot.serverID!">
            <template #depotTitle>
              <ATypographyText
                :class="`${prefixCls}__title`"
                :ellipsis="{ tooltip: true }"
                :content="depot.description || depot.prefix"
              />
              <div class="ml">
                <BasicButton type="text" size="small" :disabled="!index " class="border-0 bg-FO-Container-Fill0!" @click.stop="depotsSort(depot.ID, 0)">
                  <Icon icon="icon-park-outline:arrow-circle-up" />
                </BasicButton>
                <BasicButton type="text" size="small" :disabled="index === depotList.length - 1 " class="border-0 bg-FO-Container-Fill0!" @click.stop="depotsSort(depot.ID, 1)">
                  <Icon icon="icon-park-outline:arrow-circle-down" />
                </BasicButton>
              </div>
              <div v-if="userStore.isSuperAdmin">
                <BasicButton type="text" size="small" @click.stop="handleEdit(depot)">
                  <Icon icon="icon-park-outline:edit" />
                </BasicButton>
                <APopconfirm
                  :title="`确认删除仓库『${depot.description || depot.prefix}』吗？`"
                  @confirm.stop="handleDelete(depot)"
                >
                  <BasicButton type="text" danger size="small" @click.stop>
                    <Icon icon="icon-park-outline:delete" />
                  </BasicButton>
                </APopconfirm>
              </div>
              <BasicButton
                v-if="hasPermissionPerforceManagement"
                v-tippy="{
                  content: '配置根目录级别的P4访问权限，用于允许或限制项目组成员目录的访问',
                  placement: 'bottom',
                }"
                class="custom-rounded-btn mx-3"
                noIcon
                @click="handleGo('PerforceManagement', depot)"
              >
                P4权限配置
              </BasicButton>
              <BasicButton
                v-if="hasPermissionP4Triggers"
                class="custom-rounded-btn"
                noIcon
                @click="handleGo('P4Triggers', depot)"
              >
                P4Trigger配置
              </BasicButton>
              <BasicButton
                v-if="userStore.isDM01"
                :class="`${prefixCls}__manage-btn`"
                shape="round"
                size="small"
                @click.stop="handleGo('DM01GroupManagement', depot)"
              >
                <Icon icon="icon-park-solid:peoples" />
                管理员配置
              </BasicButton>
            </template>
          </P4Stream>
        </div>
      </transition-group>
    </div>
    <div v-else class="m-4 rounded-lg bg-FO-Container-Fill1 p-4">
      <AEmpty :image="emptyImg" description="该项目未配置仓库">
        <BasicButton type="primary" @click="handleCreate()">
          新增仓库
        </BasicButton>
      </AEmpty>
    </div>

    <P4DepotsDrawer @register="registerDrawer" @success="handleSuccess" />
  </div>
</template>

<script lang="ts" setup>
import {
  Empty as AEmpty,
  Popconfirm as APopconfirm,
  TypographyText as ATypographyText,
} from 'ant-design-vue';
import { SettingConfig } from '@icon-park/vue-next';
import { AppHelpDoc, AppProjectSelect } from '/@/components/Application';
import { computed, onBeforeMount, onMounted, unref, watch } from 'vue';
import { useRouter } from 'vue-router';
import P4DepotsDrawer from './P4DepotsDrawer.vue';
import { useP4Depot } from './hook';
import P4Stream from './streams/index.vue';
import type { DepotsListItem } from '/@/api/page/model/p4Model';
import { deleteDepot, updateDepotsSort } from '/@/api/page/p4';
import { useDrawer } from '/@/components/Drawer';
import { Icon } from '/@/components/Icon';
import { useDesign } from '/@/hooks/web/useDesign';
import { useGo } from '/@/hooks/web/usePage';
import { useUserStoreWithOut } from '/@/store/modules/user';
import { usePermissionCheckPoint } from '../../../service/permission/usePermission.ts';
import { ForgeonHeader, PermissionPoint, PlatformEnterPoint, useForgeOnSider } from '@hg-tech/oasis-common';
import { BasicButton } from '../../../components/Button';
import { useHeaderSetting } from '/@/hooks/setting/useHeaderSetting.ts';
import { useRootSetting } from '/@/hooks/setting/useRootSetting.ts';
import { FORGEON_MENU_FOLDED_KEY } from '/@/enums/cacheEnum.ts';
import { Persistent } from '/@/utils/cache/persistent.ts';

const { prefixCls } = useDesign('p4-depots');
const { currentRoute } = useRouter();
const router = useRouter();
const [registerDrawer, { openDrawer }] = useDrawer();
const { getDarkMode } = useRootSetting();
const { collapsed, showSubSider } = useForgeOnSider();
const { getShowDoc, getShowProjectSelect } = useHeaderSetting();
const emptyImg = AEmpty.PRESENTED_IMAGE_SIMPLE;
const go = useGo();
const userStore = useUserStoreWithOut();
const { depotList, getDepotsList } = useP4Depot();
const [hasPermissionPerforceManagement] = usePermissionCheckPoint(PermissionPoint.PerforceManagement);
const [hasPermissionP4Triggers] = usePermissionCheckPoint(PermissionPoint.P4Triggers);
const hasToken = userStore.getToken;
const getShowMenuUnfoldBtn = computed(() => {
  return unref(collapsed) && unref(showSubSider);
});

function onHandleExpand() {
  collapsed.value = false;
  Persistent.setLocal(FORGEON_MENU_FOLDED_KEY, '0');
}

function handleToManager() {
  router.push({ name: PlatformEnterPoint.ProjectsManagement });
}

function handleCreate() {
  openDrawer(true, {
    isUpdate: false,
  });
}

function handleEdit(record: DepotsListItem) {
  openDrawer(true, {
    record,
    isUpdate: true,
  });
}

async function handleDelete(record: DepotsListItem) {
  await deleteDepot(userStore.getProjectId!, record.ID!);
  await getDepotsList();
}

function handleSuccess() {
  getDepotsList();
}

function handleGo(name: string, depot: DepotsListItem) {
  go({ name, params: { id: depot.ID }, query: { sID: depot.serverID } });
}

async function init() {
  if (!userStore.projectId) {
    return;
  }
  await getDepotsList();
}

watch(
  () => userStore.getProjectId,
  (v, oldValue) => {
    if (v && v !== oldValue) {
      init();
    }
  },
);

async function depotsSort(id: number | undefined, type: number) {
  if (!id) {
    return;
  }
  // 排序，获取id
  const depotIdList = depotList.value.map((item) => item.ID!);
  const index = depotIdList.indexOf(id);
  if (type) {
    /*
     * 下移
     * 剔除最后一个
     */
    if (index === depotIdList.length - 1) {
      return;
    }
    const temp = depotIdList[index + 1];
    depotIdList[index + 1] = id;
    depotIdList[index] = temp;
  } else {
    // 剔除第一个
    if (index === 0) {
      return;
    }
    const temp = depotIdList[index - 1];
    depotIdList[index - 1] = id;
    depotIdList[index] = temp;
  }
  await updateDepotsSort(userStore.getProjectId!, { idList: depotIdList });
  init();
}

onBeforeMount(() => {
  init();
});

onMounted(() => {
  // 判断url是否需要打开新增表单
  const isAdd = unref(currentRoute).query.isAdd;
  if (isAdd) {
    handleCreate();
  }
});
</script>

<style lang="less">
@prefix-cls: ~'hypergryph-p4-depots';

.@{prefix-cls} {
  &__header {
    padding: 16px;
    background-color: @FO-Container-Fill1;
  }

  &__body {
    padding: 20px;
  }

  &__card {
    margin-bottom: 16px;
    padding: 16px;
    border-radius: 8px;
    background-color: @FO-Container-Fill1;
  }

  &__manage-btn {
    margin-left: 12px;
    border: none;
    border-radius: 6px;
    background: #565656 !important;
    color: #fff !important;
  }

  &__title {
    max-width: 300px !important;
    font-size: 16px;
    font-weight: bold;
  }
}
.flip-list-move {
  transition: transform 0.5s;
}
</style>
