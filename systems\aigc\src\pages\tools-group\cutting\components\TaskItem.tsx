import { FileUploader } from '@/common/components/file-uploader';
import { BasicFillAISlice, CheckCircleFilled, Error, Loading3QuartersOutlined, SliceEdit, SystemFillClose, SystemStrokeClock } from '@/common/components/svg-icons';
import { useNaiveUIApi } from '@/common/hooks';
import { type CuttingTask, type CuttingTaskType, type TaskImageItem, CuttingTaskStatus, cuttingTaskTypeMap, ImageHandlingState } from '@/models/cutting';
import { type UploadFileInfo, NButton, NIcon } from 'naive-ui';
import { type PropType, defineComponent, nextTick, watch } from 'vue';
import { DisplayCuttingImage } from './DisplayCuttingImage';
import { UploadScene } from '@/apis/oss.api';
import { throttle } from 'lodash';
import { ForgeonThemeCssVar } from '@hg-tech/forgeon-style';

import style from './TaskItem.module.less';

const iconsMap: Record<ImageHandlingState, { icon: () => JSX.Element | null; bgStyle: Record<string, string>; text?: string }> = {
  [ImageHandlingState.COMPLETED]: {
    icon: () => <CheckCircleFilled />,
    bgStyle: { backgroundColor: ForgeonThemeCssVar.ContainerFill1, color: ForgeonThemeCssVar.BrandPrimaryDefault },
  },
  [ImageHandlingState.EDITED]: {
    icon: () => <SliceEdit />,
    bgStyle: { backgroundColor: ForgeonThemeCssVar.ContainerFill1, color: ForgeonThemeCssVar.BrandPrimaryDefault },
  },
  [ImageHandlingState.FINISHED]: {
    icon: () => <BasicFillAISlice />,
    bgStyle: { backgroundColor: ForgeonThemeCssVar.ContainerFill1, color: ForgeonThemeCssVar.BrandPrimaryDefault },
  },
  [ImageHandlingState.CANCELED]: {
    icon: () => <BasicFillAISlice />,
    bgStyle: { backgroundColor: ForgeonThemeCssVar.ContainerFill3, color: ForgeonThemeCssVar.ContentIcon3 },
  },
  [ImageHandlingState.FAILURE]: {
    icon: () => <Error />,
    bgStyle: { backgroundColor: ForgeonThemeCssVar.ContainerFill1, color: ForgeonThemeCssVar.FunctionalWarning1Default },
    text: '自动切图失败，请选择后手动调整',
  },
  [ImageHandlingState.PROCESSING]: {
    icon: () => <BasicFillAISlice />,
    bgStyle: { backgroundColor: ForgeonThemeCssVar.BrandSecondaryDefault, color: ForgeonThemeCssVar.BrandPrimaryDefault },
  },
  [ImageHandlingState.SLICING]: {
    icon: () => <BasicFillAISlice />,
    bgStyle: { backgroundColor: ForgeonThemeCssVar.BrandSecondaryDefault, color: ForgeonThemeCssVar.BrandPrimaryDefault },
  },
  [ImageHandlingState.PENDING]: {
    icon: () => null,
    bgStyle: {},
  },
  [ImageHandlingState.ERROR]: {
    icon: () => null,
    bgStyle: {},
  },
};

const TaskItem = defineComponent({
  props: {
    task: {
      type: Object as PropType<CuttingTask>,
      default: () => {},
    },
    activeKey: {
      type: String as PropType<string>,
      default: '',
    },
    isDrawing: {
      type: Boolean as PropType<boolean>,
      default: false,
    },
    onUpdateFiles: {
      type: Function as PropType<(files: UploadFileInfo, index: number) => void>,
      default: () => {},
    },
    onRemoveImage: {
      type: Function as PropType<(id: string) => void>,
      default: () => {},
    },
    onTaskTypeChange: {
      type: Function as PropType<(type: CuttingTaskType) => void>,
      default: () => {},
    },
    onActivated: {
      type: Function as PropType<(taskId: string, image: TaskImageItem) => void>,
      default: () => {},
    },
    taskTypeList: {
      type: Array as PropType<CuttingTaskType[]>,
      default: () => [],
    },
    onScrollToActive: {
      type: Function as PropType<(y: number) => void>,
      default: () => {},
    },
  },
  setup(props) {
    const { message } = useNaiveUIApi();

    const onTaskTypeChange = throttle((type: CuttingTaskType) => {
      if (props.task.status !== CuttingTaskStatus.PENDING) {
        message.warning('当前任务已开始处理，无法修改任务类型');
        return;
      }
      if (props.isDrawing) {
        message.warning('当前正在绘制，无法修改任务类型');
        return;
      }
      props.onTaskTypeChange(type);
    }, 200);

    watch(() => props.activeKey, (newKey, oldKey) => {
      if (!newKey || newKey === oldKey) {
        return;
      }
      nextTick(() => {
        const activeEl = document.querySelector(`.task-item-wrapper .${style.active}`) as HTMLElement;
        if (activeEl) {
          props.onScrollToActive(activeEl.getBoundingClientRect().top);
        }
      });
    });

    const renderPendingTask = () => {
      return (
        <div class={style.taskItemContent}>
          <div class="grid grid-cols-3 mb-24px gap-16px">
            {props.task.images.length > 0
              ? props.task.images.map((item) => (
                <div key={item.id}>
                  <FileUploader
                    accept="image/png"
                    compressDisabled
                    containerStyle={{
                      width: '100px',
                      height: '100px',
                      borderRadius: '16px',
                    }}
                    fileList={item.basicImage ? [item.basicImage] : []}
                    fileName={item.basicImage?.name || ''}
                    maxSize={200}
                    nameFormat={(name: string, uuid: string, extension: string) => {
                      return `${name}_【${uuid}】.${extension}`;
                    }}
                    onUpdate:fileList={(files) => {
                      const index = props.task.images.findIndex((i) => i.id === item.id);
                      if (index !== -1) {
                        if (files.length === 0) {
                          props.onRemoveImage(item.id);
                          return;
                        }
                        props.onUpdateFiles(files[0], index);
                      }
                    }}
                    ossScene={UploadScene.CropImage}
                    placeholder="点击或者拖拽图片到该区域"
                    showTools={['preview', 'reupload', 'delete']}
                    uploadType="oss"
                    useOriginName
                  />
                </div>
              ))
              : null}
          </div>
          <div class="c-text-gray1 FO-Font-B16 mb-12px">
            <span class="mr-4px c-FO-Brand-Primary-Default">Step 2.</span>
            任务类型
          </div>
          <div class="flex flex-wrap items-center gap-8px">
            {props.taskTypeList.map((type) => (
              <NButton
                class={['cursor-pointer rd-5px px-12px', props.task.type !== type && 'c-FO-Content-Text2']}
                onClick={() => {
                  onTaskTypeChange(type as CuttingTaskType);
                }}
                secondary
                size="small"
                type={props.task.type === type ? 'primary' : 'default'}
              >
                {cuttingTaskTypeMap[type as CuttingTaskType]}
              </NButton>
            ))}
          </div>
        </div>
      );
    };

    const renderOperationsIcon = (state: ImageHandlingState) => {
      return (
        <div class="flex-c-center absolute right-4px top-4px rd-6px p-4px" style={iconsMap[state]?.bgStyle}>
          <NIcon size={16}>
            {iconsMap[state]?.icon()}
          </NIcon>
          {iconsMap[state]?.text && <div class="ml-4px c-FO-Content-Text1">{iconsMap[state]?.text}</div>}
        </div>
      );
    };

    const renderProcessingTask = () => {
      return (
        <div class="task-item-wrapper">
          <div class="Fo-Font-B14 mb-12px c-FO-Content-Text1">
            {
              props.task.safeImages?.map((imgObj) => {
                return (
                  <div
                    class={[
                      style.taskContent,
                      'mb-16px h-200px rd-lg bg-FO-Container-Fill2 p-12px',
                      props.activeKey === imgObj.name ? style.active : '',
                    ]}
                    key={imgObj.name}
                    onClick={() => {
                      props.onActivated(props.task.id, imgObj);
                    }}
                  >
                    {
                      imgObj.state === ImageHandlingState.PROCESSING && (
                        <div class="h-full w-full flex items-center justify-center c-FO-Content-Text3">
                          <NIcon class="mr-4px" size={16}>
                            <SystemStrokeClock />
                          </NIcon>
                          <div>待处理</div>
                        </div>
                      )
                    }
                    {
                      imgObj.state === ImageHandlingState.SLICING && (
                        <div class="h-full w-full flex flex-col items-center justify-center c-FO-Content-Text3">
                          <NIcon class="mb-12px" size={32}>
                            <Loading3QuartersOutlined class="animate-spin" />
                          </NIcon>
                          <div>自动切图中...</div>
                        </div>
                      )
                    }
                    {
                      imgObj.state === ImageHandlingState.ERROR && (
                        <div class="flex-c-center h-full w-full c-FO-Content-Text3">
                          <div>自动切图发生错误，请重新上传后重试</div>
                        </div>
                      )
                    }
                    {
                      imgObj.state === ImageHandlingState.CANCELED && (
                        <div class="flex-c-center h-full w-full c-FO-Content-Text3">
                          <NIcon class="mr-6px" size={16}>
                            <SystemFillClose />
                          </NIcon>
                          <div>用户已取消</div>
                        </div>
                      )
                    }
                    {
                      [ImageHandlingState.COMPLETED, ImageHandlingState.EDITED, ImageHandlingState.FAILURE, ImageHandlingState.FINISHED].includes(imgObj.state) && (
                        <DisplayCuttingImage
                          operation={imgObj.targetOperations || imgObj.operations}
                          src={imgObj.basicImage?.thumbnailUrl || imgObj.basicImage?.url || ''}
                        />
                      )
                    }
                    {renderOperationsIcon(imgObj.state)}
                  </div>
                );
              })
            }
          </div>
        </div>
      );
    };

    return () => (
      <div class={style.taskItem}>
        {props.task.status === CuttingTaskStatus.PENDING
          ? renderPendingTask()
          : renderProcessingTask()}
      </div>
    );
  },
});

export {
  TaskItem,
};
