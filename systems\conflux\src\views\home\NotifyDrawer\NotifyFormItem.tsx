import { type ApiNotifyV1NotifyRule, ApiNotifyV1NotifyScheduleType, ApiNotifyV1NotifyTargetType } from '@hg-tech/api-schema-merge';
import { FormItem, Select, Switch, TimePicker } from 'ant-design-vue';
import { NotifyScheduleTypeOptions } from '../../../models/config.model';
import { type PropType, computed, defineComponent } from 'vue';
import dayjs from 'dayjs';
import { type ForgeonUserSelectorOptionItem, ForgeonUserSelector, RadioButton } from '@hg-tech/oasis-common';
import Icon from '@ant-design/icons-vue';
import type { Rule } from 'ant-design-vue/es/form';
import { DragItem } from '../../../components/DragItem';
import { useUserListOption } from '../../../composables/useUserSearch';

import Delete from '../../../assets/svg/Delete.svg?component';
import SystemStrokeDrag from '../../../assets/svg/SystemStrokeDrag.svg?component';

const NotifyFormItem = defineComponent({
  props: {
    domain: {
      type: String as PropType<string>,
      required: true,
    },
    index: {
      type: Number as PropType<number>,
      required: true,
    },
    content: {
      type: Object as PropType<ApiNotifyV1NotifyRule>,
      default: () => { },
    },
    originContent: {
      type: Object as PropType<ApiNotifyV1NotifyRule>,
      default: () => { },
    },
    chatOptions: {
      type: Array as PropType<Array<ForgeonUserSelectorOptionItem>>,
      default: () => [],
    },
    onUpdateContent: {
      type: Function as PropType<(value: ApiNotifyV1NotifyRule) => void>,
      required: true,
    },
    onDelete: {
      type: Function as PropType<(index: number) => void>,
      default: () => { },
    },
  },
  setup(props) {
    const { userListOptions, userListLoading, queryUser, resetUserList } = useUserListOption(
      computed(() => [
        ...(props.content.targetUser ?? []),
      ]),
    );
    const content = computed({
      get: () => props.content,
      set: (value) => {
        const newValue = { ...props.content, ...value };
        props.onUpdateContent(newValue);
      },
    });
    const timePickerValue = computed(() => {
      return dayjs(`${content.value.notifySchedule?.hour ?? 0}:${content.value.notifySchedule?.minute ?? 0}`, 'HH:mm');
    });

    const rules: Record<string, Rule[]> = {
      notifyScheduleType: [{ required: true, message: '请选择通知周期' }],
      target: [{
        validator: async (_, value) => {
          if (!value || value.length === 0) {
            return Promise.reject(new Error('请选择接收用户/群组'));
          }
          return Promise.resolve();
        },
      }],
    };

    return () => (
      <DragItem class="w-full flex gap-12px rd-6px pr-8px pt-8px" dragClass="drag-handle">
        {{
          deaultDrag: ({ isHover }: { isHover: boolean }) => (
            <FormItem>
              <div
                class="drag-handle w-30px flex justify-center"
              >
                <div class="w-50px flex items-center justify-center">
                  {
                    isHover
                      ? (
                        <Icon
                          class="cursor-move font-size-18px"
                          component={<SystemStrokeDrag />}
                        />
                      )
                      : <div class="FO-Font-B14 h-24px w-24px flex justify-center rd-full bg-FO-Datavis-Violet3 c-FO-Content-Text1">{props.index + 1}</div>
                  }
                </div>
              </div>
            </FormItem>
          ),
          default: () => (
            <>
              <FormItem name={[props.domain, props.index, 'notifySchedule', 'notifyScheduleType']} rules={rules.notifyScheduleType}>
                <Select
                  onUpdate:value={(value) => {
                    content.value = {
                      ...content.value,
                      notifySchedule: {
                        notifyScheduleType: value as ApiNotifyV1NotifyScheduleType,
                        hour: 0,
                        minute: 0,
                        weekday: value === ApiNotifyV1NotifyScheduleType.WEEKLY ? 1 : 0,
                      },
                    };
                  }}
                  placeholder="请选择周期"
                  value={content.value.notifySchedule?.notifyScheduleType}
                >
                  {NotifyScheduleTypeOptions.map((rule) => (
                    <Select.Option key={rule.value} value={rule.value}>
                      {rule.label}
                    </Select.Option>
                  ))}
                </Select>
              </FormItem>
              {
                content.value.notifySchedule?.notifyScheduleType === ApiNotifyV1NotifyScheduleType.WEEKLY && (
                  <FormItem name={[props.domain, props.index, 'notifySchedule', 'hour']}>
                    <Select
                      onUpdate:value={(value) => {
                        content.value = {
                          ...content.value,
                          notifySchedule: {
                            ...content.value.notifySchedule,
                            weekday: value as number,
                          },
                        };
                      }}
                      placeholder="请选择周几"
                      value={content.value.notifySchedule?.weekday}
                    >
                      {[...Array.from({ length: 7 }).keys()].map((day) => (
                        <Select.Option key={day} value={day + 1}>
                          {['周一', '周二', '周三', '周四', '周五', '周六', '周日'][day]}
                        </Select.Option>
                      ))}
                    </Select>
                  </FormItem>
                )
              }
              <FormItem name={[props.domain, props.index, 'notifySchedule', 'hour']}>
                <TimePicker
                  allowClear={false}
                  class="w-80px"
                  format="HH:mm"
                  onUpdate:value={(value) => {
                    const [hour, minute] = (dayjs(value)?.format('HH:mm') ?? '').split(':').map(Number);
                    content.value = {
                      ...content.value,
                      notifySchedule: {
                        ...content.value.notifySchedule,
                        hour,
                        minute,
                      },
                    };
                  }}
                  placeholder="请选择时间"
                  value={timePickerValue.value}
                />
              </FormItem>
              <FormItem>发送到</FormItem>
              <FormItem name={[props.domain, props.index, 'notifyTargetType']}>
                <RadioButton
                  onUpdateValue={(value) => {
                    content.value = {
                      ...content.value,
                      // 如果选择的目标类型与原始内容相同，则保留原始目标，否则清空目标
                      target: value === props.originContent?.notifyTargetType ? props.originContent?.target : undefined,
                      notifyTargetType: value as ApiNotifyV1NotifyTargetType,
                    };
                  }}
                  options={[
                    { value: ApiNotifyV1NotifyTargetType.USER, label: '用户', disabled: false },
                    { value: ApiNotifyV1NotifyTargetType.CHAT, label: '群组', disabled: false },
                  ]}
                  value={content.value.notifyTargetType as string}
                />
              </FormItem>
              <FormItem class="flex-1" name={[props.domain, props.index, 'target']} rules={rules.target}>
                {content.value.notifyTargetType === ApiNotifyV1NotifyTargetType.CHAT
                  ? (
                    <ForgeonUserSelector
                      filterOption={(input, option) => {
                        return option?.label.toLowerCase().includes(input.toLowerCase());
                      }}
                      maxTagCount={2}
                      multiple={true}
                      onUpdate:value={(value) => {
                        content.value = {
                          ...content.value,
                          target: value,
                        };
                      }}
                      options={props.chatOptions}
                      placeholder="请选择群组"
                      showAvatar
                      value={content.value.target}
                    />
                  )
                  : (
                    <ForgeonUserSelector
                      loading={userListLoading.value}
                      multiple
                      onReset={resetUserList}
                      onSearch={(params) => {
                        queryUser(params, {});
                      }}
                      onUpdate:value={(value) => {
                        content.value = {
                          ...content.value,
                          target: value,
                        };
                      }}
                      options={userListOptions.value}
                      placeholder="请输入用户姓名/昵称/邮箱/拼音"
                      showAvatar
                      value={content.value.target}
                    />
                  )}
              </FormItem>
              <FormItem>
                <Switch
                  checked={content.value.enable}
                  class="ml-12px v-text-bottom"
                  onUpdate:checked={(value) => {
                    content.value = {
                      ...content.value,
                      enable: value as boolean,
                    };
                  }}
                  size="small"
                />
              </FormItem>
              <FormItem>
                <Icon
                  class="cursor-pointer font-size-18px"
                  component={<Delete />}
                  onClick={() => {
                    props.onDelete(props.index);
                  }}
                />
              </FormItem>
            </>
          ),
        }}
      </DragItem>
    );
  },
});

export {
  NotifyFormItem,
};
