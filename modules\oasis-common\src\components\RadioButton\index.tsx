import { type PropType, defineComponent } from 'vue';
import type { RadioButtonOptions } from './type';
import style from './style.module.less';

const RadioButton = defineComponent({
  props: {
    value: {
      type: [String, Number, Boolean],
      required: true,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    options: {
      type: Array as PropType<Array<RadioButtonOptions>>,
      required: true,
    },
    onUpdateValue: {
      type: Function as PropType<(value: string | number | boolean) => void>,
      default: () => {},
    },
  },
  emits: ['update:value'],
  setup(props, { emit }) {
    const handleChange = (option: RadioButtonOptions) => {
      if (!props.disabled) {
        emit('update:value', option.value);
        props.onUpdateValue(option.value);
      }
    };
    return () => (
      <div class={[style.RadioButton, 'flex items-center gap-2px']}>
        {props.options.map((option) => (
          <div
            class={[style.RadioButtonItem, option.value === props.value ? style.active : null, 'FO-Font-B14']}
            onClick={() => handleChange(option)}
          >{option.label}
          </div>
        ))}
      </div>
    );
  },
});

export {
  RadioButton,
  RadioButtonOptions,
};
