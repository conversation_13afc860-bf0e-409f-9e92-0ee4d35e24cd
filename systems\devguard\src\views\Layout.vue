<template>
  <div class="devguard-layout">
    <ForgeonHeader
      :onHandleMenuExpand="onHandleMenuExpand"
      :showUnfoldIcon="showUnfoldIcon"
      title="提交中心"
    />
    <router-view />
  </div>
</template>

<script lang="ts" setup>
import { ForgeonHeader, useMicroAppInject, usePlatformConfigCtx } from '@hg-tech/oasis-common';
import { computed } from 'vue';

const platformConfig = computed(() => {
  if (window.__MICRO_APP_ENVIRONMENT__) {
    return useMicroAppInject(usePlatformConfigCtx);
  } else {
    return null;
  }
});

const showUnfoldIcon = computed(() => {
  if (platformConfig.value?.data.value) {
    return !platformConfig.value.data.value.isMenuExpanded;
  } else {
    return false;
  }
});

function onHandleMenuExpand() {
  platformConfig.value?.data.value?.changeMenuExpendStatus(true);
}
</script>
