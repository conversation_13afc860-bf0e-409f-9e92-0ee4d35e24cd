import { type PropType, defineComponent, onMounted, ref, watchEffect } from 'vue';

const DragItem = defineComponent({
  props: {
    dragClass: {
      type: String as PropType<string>,
      default: 'drag-handle',
    },
  },
  setup(props, { slots }) {
    const isHover = ref(false);
    const containerRef = ref<HTMLElement>();
    const removeEventListener = () => {
      containerRef.value?.removeEventListener('mouseleave', () => {
        isHover.value = false;
      });
      containerRef.value?.removeEventListener('dragend', () => {
        isHover.value = false;
      });
      containerRef.value?.removeEventListener('mouseenter', () => {
        isHover.value = true;
      });
    };

    const addEventListener = () => {
      containerRef.value?.addEventListener('mouseleave', () => {
        isHover.value = false;
      });
      containerRef.value?.addEventListener('dragend', () => {
        isHover.value = false;
      });
      containerRef.value?.addEventListener('mouseenter', () => {
        isHover.value = true;
      });
    };

    watchEffect((onCleanup) => {
      onCleanup(() => {
        removeEventListener();
      });
    });
    onMounted(async () => {
      isHover.value = false;
      setTimeout(() => {
        addEventListener();
      }, 0);
    });

    return () => (
      <div
        class="flex items-center"
        ref={containerRef}
      >
        <div class={['w-30px flex justify-center', props.dragClass]}>
          {slots.deaultDrag?.({ isHover: isHover.value })}
        </div>
        {slots.default ? slots.default() : null}
      </div>
    );
  },
});
export {
  DragItem,
};
