import type { Router } from 'vue-router';
import { setTrackCommonParams, traceRouteChange } from '/@/service/tracker';
import { nextTick } from 'vue';

export function withTrack(router: Router) {
  router.beforeResolve((to, from) => {
    setTrackCommonParams({
      route_path: to.path || '',
      prv_route_path: from.path as string,
    });
  });
  router.afterEach(async (to, from) => {
    await nextTick(); // 等待页面渲染完成，以及能获取到正确的 title
    if (to.meta.microApp?.selfTrack && to.meta.microApp.name === to.name) {
      // 如果是子应用且配置了 selfTrack，则不进行路由埋点上报
      return;
    }
    traceRouteChange({
      data: {
        route_name: to.name as string,
        route_path: to.path,
        prv_route_name: from.name as string,
        prv_route_path: from.path,
      },
    });
  });
}
