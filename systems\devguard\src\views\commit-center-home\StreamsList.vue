<template>
  <div class="streams-list">
    <div v-for="stream in listSubmit" :key="stream.ID" class="streams-list-item">
      <div class="flex items-center">
        <MainlineIcon v-if="stream.streamType === StreamType.Mainline" class="h-20px" />
        <ReleaseIcon v-else-if="stream.streamType === StreamType.Release" class="h-20px" />
        <TaskIcon v-else-if="stream.streamType === StreamType.Task" class="h-20px" />
        <VirtualIcon v-else-if="stream.streamType === StreamType.Virtual" class="h-20px" />
        <DevelopmentIcon v-else class="h-20px" />

        <div class="ml-16px">
          <div class="flex items-center font-bold">
            {{ stream.description || stream.path }}
          </div>
          <div class="flex items-center text-xs c-FO-Content-Text2">
            <span>{{ stream.path }}</span>
          </div>
        </div>
      </div>
      <div class="flex">
        <div class="b-r-1px b-r-FO-Container-Stroke4 px-10px">
          <div class="flex cursor-pointer items-center gap-10px b-rd-8px p-10px hover:bg-FO-Container-Fill2">
            <div class="FO-Font-b18">
              正在提交
            </div>
            <div class="FO-Font-B18">
              {{ stream.submittingCount }}
            </div>
          </div>
        </div>
        <div class="b-r-1px b-r-FO-Container-Stroke4 px-10px">
          <div class="flex cursor-pointer items-center gap-10px b-rd-8px p-10px hover:bg-FO-Container-Fill2">
            <div class="FO-Font-b18">
              检查实例状态
            </div>

            <div
              v-for="(item, index) in stream.instance.filter((instanceItem) => instanceItem.compile)" :key="index"
              class="h-10px w-10px b-rd-5px"
              :class="{ 'bg-FO-Datavis-Yellow2': item.status === 1, 'bg-FO-Datavis-Blue2': item.status === 2, 'bg-FO-Content-Text4': item.status === 3 }"
            />
            <div class="h-20px w-1px bg-FO-Container-Stroke4" />
            <div
              v-for="(item, index) in stream.instance.filter((instanceItem) => !instanceItem.compile)" :key="index"
              class="h-10px w-10px b-rd-5px"
              :class="{ 'bg-FO-Datavis-Yellow2': item.status === 1, 'bg-FO-Datavis-Blue2': item.status === 2, 'bg-FO-Content-Text4': item.status === 3 }"
            />
            <div class="FO-Font-b18">
              待检查队列
            </div>
            <div class="FO-Font-B18">
              {{ stream.instanceCount }}
            </div>
          </div>
        </div>
        <div class="px-10px">
          <div class="flex cursor-pointer items-center gap-10px b-rd-8px p-10px hover:bg-FO-Container-Fill2">
            <div class="FO-Font-b18">
              今日已提交
            </div>
            <div class="FO-Font-B18">
              {{ stream.todaySubmittedCount }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { type StreamsListItem, getListSubmit } from '../../api';
import DevelopmentIcon from '../../assets/icons/stream-development.svg?component';
import MainlineIcon from '../../assets/icons/stream-mainline.svg?component';
import ReleaseIcon from '../../assets/icons/stream-release.svg?component';
import TaskIcon from '../../assets/icons/stream-task.svg?component';
import VirtualIcon from '../../assets/icons/stream-virtual.svg?component';
import { StreamType } from './steams.data';
import { onMounted, ref } from 'vue';

const props = withDefaults(defineProps<{
  deptPrefix: string;

}>(), {
  deptPrefix: '',
});
const listSubmit = ref<StreamsListItem[]>([]);
onMounted(async () => {
  const res = await getListSubmit({ iid: 1, deptPrefix: props.deptPrefix }, {});
  listSubmit.value = res.data?.data?.list;
});
</script>

<style lang="less" scoped>
@import (reference) '@hg-tech/forgeon-style/vars.less';

.streams-list {
  &-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 16px;
    border: 1px solid @FO-Container-Stroke1;
    border-radius: 8px;
    background-color: @FO-Container-Fill1;

    &:hover {
      border-color: @FO-Brand-Primary-Default;
    }

    &:not(:last-child) {
      margin-bottom: 8px;
    }
  }
}
</style>
