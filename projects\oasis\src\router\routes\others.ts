import type { RouteRecordRaw } from 'vue-router';
import { PermissionPoint, PlatformEnterPoint, PlatformRoutePath } from '@hg-tech/oasis-common';

// TODO 未整理的路由
export const otherRoutes: Readonly<RouteRecordRaw[]> = [
  {
    name: PlatformEnterPoint.CrashCollect,
    path: PlatformRoutePath.CrashCollect,
    component: () => import('../../views/crashCollect/index.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.CrashCollect],
      },
      title: 'Crash平台',
    },
  },
  {
    name: PlatformEnterPoint.CrashDetail,
    path: PlatformRoutePath.CrashDetail,
    component: () => import('../../views/crashCollect/CrashDetail.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.CrashDetail],
      },
      title: 'Crash详情',
    },
  },
  {
    name: PlatformEnterPoint.CrashClassDetail,
    path: PlatformRoutePath.CrashClassDetail,
    component: () => import('../../views/crashCollect/CrashClassDetail.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.CrashClassDetail],
      },
      title: 'Crash分类详情',
    },
  },
  {
    name: PlatformEnterPoint.ProtocolTest,
    path: PlatformRoutePath.ProtocolTest,
    redirect: {
      name: PlatformEnterPoint.ProtocolTestDevices,
    },
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.ProtocolTestDevices],
      },
      title: '协议测试平台',
    },
  },
  {
    name: PlatformEnterPoint.ProtocolTestDevices,
    path: PlatformRoutePath.ProtocolTestDevices,
    component: () => import('../../views/protocol/devices/index.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.ProtocolTestDevices],
      },
      title: '设备列表',
    },
  },
  {
    name: PlatformEnterPoint.ProtocolTestDetail,
    path: PlatformRoutePath.ProtocolTestDetail,
    component: () => import('../../views/protocol/detail/index.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.ProtocolTestDetail],
      },
      title: '协议测试详情页',
    },
  },
  {
    name: PlatformEnterPoint.ProtocolTestHistory,
    path: PlatformRoutePath.ProtocolTestHistory,
    component: () => import('../../views/protocol/history/index.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.ProtocolTestHistory],
      },
      title: '协议测试记录',
    },
  },
  {
    name: PlatformEnterPoint.Automation,
    path: PlatformRoutePath.Automation,
    redirect: {
      name: PlatformEnterPoint.AutomationReport,
    },
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.AutomationReport],
      },
      title: '自动化测试',
    },
  },
  {
    name: PlatformEnterPoint.AutomationReport,
    path: PlatformRoutePath.AutomationReport,
    component: () => import('../../views/automation/report/index.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.AutomationReport],
      },
      title: '报告',
    },
  },
  {
    name: PlatformEnterPoint.AutomationTask,
    path: PlatformRoutePath.AutomationTask,
    component: () => import('../../views/automation/task/index.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.AutomationTask],
      },
      title: '任务配置',
    },
  },
  {
    name: PlatformEnterPoint.AutomationSet,
    path: PlatformRoutePath.AutomationSet,
    component: () => import('../../views/automation/set/index.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.AutomationSet],
      },
      title: '用例集配置',
    },
  },
  {
    name: PlatformEnterPoint.Performance,
    path: PlatformRoutePath.Performance,
    redirect: {
      name: PlatformEnterPoint.PerformanceCase,
    },
    meta: {
      title: '性能数据',
    },
  },
  {
    name: PlatformEnterPoint.PerformanceCase,
    path: PlatformRoutePath.PerformanceCase,
    component: () => import('../../views/performance/case/index.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.PerformanceCase],
      },
      title: 'Perfdog性能',
    },
  },
  {
    name: PlatformEnterPoint.PerformanceCaseDetail,
    path: PlatformRoutePath.PerformanceCaseDetail,
    component: () => import('../../views/performance/case/detail/index.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.PerformanceCaseDetail],
      },
      title: 'Perfdog性能报告',
    },
  },
  {
    name: PlatformEnterPoint.PerformanceCaseCompare,
    path: PlatformRoutePath.PerformanceCaseCompare,
    component: () => import('../../views/performance/case/compare/index.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.PerformanceCaseCompare],
      },
      title: 'Perfdog性能报告对比',
    },
  },
  {
    name: PlatformEnterPoint.PerformanceReference,
    path: PlatformRoutePath.PerformanceReference,
    component: () => import('../../views/performance/case/index.vue'),
    props: () => ({
      isRefPage: true,
    }),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.PerformanceReference],
      },
      title: '性能参照',
    },
  },
  {
    name: PlatformEnterPoint.PerfdeepCase,
    path: PlatformRoutePath.PerfdeepCase,
    component: () => import('../../views/perfdeep/case/index.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.PerfdeepCase],
      },
      title: 'Unity性能',
    },
  },
  {
    name: PlatformEnterPoint.PerfdeepCaseDetail,
    path: PlatformRoutePath.PerfdeepCaseDetail,
    component: () => import('../../views/perfdeep/case/detail/index.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.PerfdeepCaseDetail],
      },
      title: 'Unity性能报告',
    },
  },
  {
    name: PlatformEnterPoint.UnrealCase,
    path: PlatformRoutePath.UnrealCase,
    component: () => import('../../views/unreal/case/index.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.UnrealCase],
      },
      title: 'Unreal性能',
    },
  },
  {
    name: PlatformEnterPoint.UnrealCaseDetail,
    path: PlatformRoutePath.UnrealCaseDetail,
    component: () => import('../../views/unreal/case/detail/index.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.UnrealCaseDetail],
      },
      title: 'Unreal性能报告',
    },
  },
  {
    name: PlatformEnterPoint.PerformanceMap,
    path: PlatformRoutePath.PerformanceMap,
    component: () => import('../../views/performance/map/index.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.PerformanceMap],
      },
      title: '性能地图',
    },
  },
  {
    name: PlatformEnterPoint.PerformanceHeatMapList,
    path: PlatformRoutePath.PerformanceHeatMapList,
    component: () => import('../../views/performance/heatMap/list/index.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.PerformanceHeatMap],
      },
      title: '热力图',
    },
  },
  {
    name: PlatformEnterPoint.PerformanceHeatMap,
    path: PlatformRoutePath.PerformanceHeatMap,
    component: () => import('../../views/performance/heatMap/index.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.PerformanceHeatMap],
      },
      title: '热力图详情',
    },
  },
  {
    name: PlatformEnterPoint.PerformanceHeatMapSettings,
    path: PlatformRoutePath.PerformanceHeatMapSettings,
    component: () => import('../../views/performance/heatMap/settings/index.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.PerformanceHeatMapSettings],
      },
      title: '热力图设置',
    },
  },
  {
    name: PlatformEnterPoint.Secure,
    path: PlatformRoutePath.Secure,
    redirect: {
      name: PlatformEnterPoint.SecureProtections,
    },
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.SecureProtections],
      },
      title: '游戏安全',
    },
  },
  {
    name: PlatformEnterPoint.SecureProtections,
    path: PlatformRoutePath.SecureProtections,
    component: () => import('../../views/secure/protections/index.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.SecureProtections],
      },
      title: '防重签',
    },
  },
  {
    name: PlatformEnterPoint.BugRobot,
    path: PlatformRoutePath.BugRobot,
    redirect: {
      name: PlatformEnterPoint.BugRobotChats,
    },
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.BugRobotChats],
      },
      title: 'BUG姬',
    },
  },
  {
    name: PlatformEnterPoint.BugRobotChats,
    path: PlatformRoutePath.BugRobotChats,
    component: () => import('../../views/test/bugRobot/index.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.BugRobotChats],
      },
      title: '群聊',
    },
  },
  {
    name: PlatformEnterPoint.PerformanceCard,
    path: PlatformRoutePath.PerformanceCard,
    redirect: {
      name: PlatformEnterPoint.PerformanceCardTrend,
    },
    meta: {
      title: '性能卡片',
    },
  },
  {
    name: PlatformEnterPoint.PerformanceCardTrend,
    path: PlatformRoutePath.PerformanceCardTrend,
    component: () => import('../../views/performance/card/trend/index.vue'),
    meta: {
      title: 'Perfdog性能卡片趋势',
    },
  },
  {
    name: PlatformEnterPoint.PerformanceCardCompare,
    path: PlatformRoutePath.PerformanceCardCompare,
    component: () => import('../../views/performance/card/compare/index.vue'),
    meta: {
      title: 'Perfdog性能卡片对比',
    },
  },
  {
    name: PlatformEnterPoint.PerfdeepCardTrend,
    path: PlatformRoutePath.PerfdeepCardTrend,
    component: () => import('../../views/perfdeep/card/trend/index.vue'),
    meta: {
      title: 'Unity性能卡片趋势',
    },
  },
  {
    name: PlatformEnterPoint.PerfdeepCardCompare,
    path: PlatformRoutePath.PerfdeepCardCompare,
    component: () => import('../../views/perfdeep/card/compare/index.vue'),
    meta: {
      title: 'Unity性能卡片对比',
    },
  },
  {
    name: PlatformEnterPoint.DevGuard,
    path: PlatformRoutePath.DevGuard,
    redirect: {
      name: PlatformEnterPoint.P4Depots,
    },
    meta: {
      title: 'DevGuard',
    },
  },
  {
    name: PlatformEnterPoint.P4PermissionManagement,
    path: PlatformRoutePath.P4PermissionManagement,
    component: () => import('../../views/versionControl/p4PermissionManage/submitPermission/index.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.P4PermissionManagement],
      },
      title: '提交权限',
    },
  },
  {
    name: PlatformEnterPoint.P4ClLabelManagement,
    path: PlatformRoutePath.P4ClLabelManagement,
    component: () => import('../../views/versionControl/clLabelManage/index.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.P4ClLabelManagement],
      },
      title: 'CL标签',
    },
  },
  {
    name: PlatformEnterPoint.P4CommitParamsConfigs,
    path: PlatformRoutePath.P4CommitParamsConfigs,
    component: () => import('../../views/versionControl/p4PermissionManage/CommitParams/Entry.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.P4Depots],
      },
      title: '提交单号',
    },
  },
  {
    name: PlatformEnterPoint.P4CommitTagConfigs,
    path: PlatformRoutePath.P4CommitTagConfigs,
    component: () => import('../../views/versionControl/p4PermissionManage/commitTag/Entry.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.P4Depots],
      },
      title: '提交tag',
    },
  },
  {
    name: PlatformEnterPoint.P4CompleteNoticeManagement,
    path: PlatformRoutePath.P4CompleteNoticeManagement,
    component: () => import('../../views/versionControl/p4CompleteNoticeManage/index.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.P4CompleteNoticeManagement],
      },
      title: '提交完成通知',
    },
  },
  {
    name: PlatformEnterPoint.P4FormDiff,
    path: PlatformRoutePath.P4FormDiff,
    component: () => import('../../views/versionControl/formDiff/index.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.P4FormDiff],
      },
      title: '表格Diff',
    },
  },
  {
    name: PlatformEnterPoint.P4FormDiffDetail,
    path: PlatformRoutePath.P4FormDiffDetail,
    component: () => import('../../views/versionControl/formDiff/detail/index.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.P4FormDiffDetail],
      },
      title: '表格Diff详情',
    },
  },
  {
    name: PlatformEnterPoint.DM01GroupManagement,
    path: PlatformRoutePath.DM01GroupManagement,
    component: () => import('../../views/versionControl/p4PermissionManage/manage/index.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.DM01GroupManagement],
      },
      title: 'DM01分组管理',
    },
  },
  {
    name: PlatformEnterPoint.SwarmSettings,
    path: PlatformRoutePath.SwarmSettings,
    component: () => import('../../views/versionControl/swarmSettings/index.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.SwarmSettings],
      },
      title: '审查和关注',
    },
  },
  {
    name: PlatformEnterPoint.ResourceCheck,
    path: PlatformRoutePath.ResourceCheck,
    component: () => import('../../views/resourceCheck/switches/index.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.ResourceCheck],
      },
      title: '提交检查配置',
    },
  },
  {
    name: PlatformEnterPoint.ResourceCheckRules,
    path: PlatformRoutePath.ResourceCheckRules,
    component: () => import('../../views/resourceCheck/rules/index.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.ResourceCheckRules],
      },
      title: '文件命名检查',
    },
  },
  {
    name: PlatformEnterPoint.ResourceCheckItems,
    path: PlatformRoutePath.ResourceCheckItems,
    component: () => import('../../views/resourceCheck/items/index.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.ResourceCheckItems],
      },
      title: '资源检查条目',
    },
  },
  {
    name: PlatformEnterPoint.ResourceCheckReports,
    path: PlatformRoutePath.ResourceCheckReports,
    component: () => import('../../views/resourceCheck/reports/index.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.ResourceCheckReports],
      },
      title: '检查报告',
    },
  },
  {
    name: PlatformEnterPoint.ResourceCheckReportDetail,
    path: PlatformRoutePath.ResourceCheckReportDetail,
    component: () => import('../../views/resourceCheck/reports/detail/index.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.ResourceCheckReportDetail],
      },
      title: '检查报告详情',
    },
  },
  {
    name: PlatformEnterPoint.ResourceCheckReportCompare,
    path: PlatformRoutePath.ResourceCheckReportCompare,
    component: () => import('../../views/resourceCheck/reports/compare/index.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.ResourceCheckReportCompare],
      },
      title: '检查报告对比',
    },
  },
  {
    name: PlatformEnterPoint.PerforceManagement,
    path: PlatformRoutePath.PerforceManagement,
    component: () => import('../../views/versionControl/perforceManagement/index.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.PerforceManagement],
      },
      title: 'P4权限配置',
    },
  },
  {
    name: PlatformEnterPoint.P4Triggers,
    path: PlatformRoutePath.P4Triggers,
    component: () => import('../../views/submitConf/p4Trigger/index.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.P4Triggers],
      },
      title: 'P4 Triggers',
    },
  },
  {
    name: PlatformEnterPoint.P4MemberManagement,
    path: PlatformRoutePath.P4MemberManagement,
    component: () => import('../../views/versionControl/p4MemberManage/index.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.P4MemberManagement],
      },
      title: '项目成员配置',
    },
  },
  {
    name: PlatformEnterPoint.P4GroupManagement,
    path: PlatformRoutePath.P4GroupManagement,
    redirect: {
      name: PlatformEnterPoint.P4CustomGroupManagement,
    },
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.P4CustomGroupManagement],
      },
      title: 'P4组配置',
    },
  },
  {
    name: PlatformEnterPoint.P4CustomGroupManagement,
    path: PlatformRoutePath.P4CustomGroupManagement,
    component: () => import('../../views/versionControl/p4GroupManage/index.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.P4CustomGroupManagement],
      },
      title: '自定义组',
    },
  },
  {
    name: PlatformEnterPoint.P4LdapGroupManagement,
    path: PlatformRoutePath.P4LdapGroupManagement,
    component: () => import('../../views/versionControl/p4GroupManage/noCustom/Ldap.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.P4LdapGroupManagement],
      },
      title: 'ldap组',
    },
  },
  {
    name: PlatformEnterPoint.ResourceCheckOld,
    path: PlatformRoutePath.ResourceCheckOld,
    redirect: {
      name: PlatformEnterPoint.ResourceCheckIndexOld,
    },
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.ResourceCheckIndexOld],
      },
      title: '提交检查配置【旧】',
    },
  },
  {
    name: PlatformEnterPoint.ResourceCheckSwitchesOld,
    path: PlatformRoutePath.ResourceCheckSwitchesOld,
    component: () => import('../../views/resourceCheckOld/switches/index.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.ResourceCheckSwitchesOld],
      },
      title: '功能开关【旧】',
    },
  },
  {
    name: PlatformEnterPoint.ResourceCheckRulesOld,
    path: PlatformRoutePath.ResourceCheckRulesOld,
    component: () => import('../../views/resourceCheckOld/rules/index.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.ResourceCheckRulesOld],
      },
      title: '命名规范【旧】',
    },
  },
  {
    name: PlatformEnterPoint.ResourceCheckIndexOld,
    path: PlatformRoutePath.ResourceCheckIndexOld,
    component: () => import('../../views/resourceCheckOld/index.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.ResourceCheckIndexOld],
      },
      title: '资源检查规则【旧】',
    },
  },
  {
    name: PlatformEnterPoint.Gitlab,
    path: PlatformRoutePath.Gitlab,
    component: () => import('../../views/versionControl/gitlab/index.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.Gitlab],
      },
      title: 'Git分支管理',
    },
  },
  {
    name: PlatformEnterPoint.GitlabReviewSettings,
    path: PlatformRoutePath.GitlabReviewSettings,
    component: () => import('../../views/versionControl/gitlab/reviewConfig/Entry.vue'),
    meta: {
      disableRouteViewKeyAttr: true,
      permissionDeclare: {
        all: [PermissionPoint.GitlabReviewSettings],
      },
      title: '审查配置',
    },
  },
  {
    name: PlatformEnterPoint.GitlabReviewList,
    path: PlatformRoutePath.GitlabReviewList,
    component: () => import('../../views/versionControl/gitlab/reviewList/ReviewContainer.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.GitlabReviewList],
      },
      title: '审查列表',
    },
  },
  {
    name: PlatformEnterPoint.GitlabSubmitDescriptionSpecifications,
    path: PlatformRoutePath.GitlabSubmitDescriptionSpecifications,
    component: () => import('../../views/versionControl/gitlab/submitDescription/index.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.GitlabSubmitDescriptionSpecifications],
      },
      title: '提交描述规范',
    },
  },
  {
    name: PlatformEnterPoint.GamePackage,
    path: PlatformRoutePath.GamePackage,
    component: () => import('../../views/test/gamePackage/list/index.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.GamePackage],
      },
      title: '游戏包体中心',
    },
  },
  {
    name: PlatformEnterPoint.GamePackageDoctor,
    path: PlatformRoutePath.GamePackageDoctor,
    component: () => import('../../views/test/gamePackage/doctor/index.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.GamePackageDoctor],
      },
      title: '游戏包体中心包体检测',
    },
  },
  {
    name: PlatformEnterPoint.GamePackageDoctorCompare,
    path: PlatformRoutePath.GamePackageDoctorCompare,
    component: () => import('../../views/test/gamePackage/doctor/compare/index.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.GamePackageDoctorCompare],
      },
      title: '游戏包体中心包体检测对比',
    },
  },
  {
    name: PlatformEnterPoint.GamePackageSettings,
    path: PlatformRoutePath.GamePackageSettings,
    component: () => import('../../views/test/gamePackage/settings/index.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.GamePackageSettings],
      },
      title: '游戏包体中心配置',
    },
  },
  {
    name: PlatformEnterPoint.GameArchive,
    path: PlatformRoutePath.GameArchive,
    component: () => import('../../views/test/gameArchive/index.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.GameArchive],
      },
      title: '游戏存档',
    },
  },
  {
    name: PlatformEnterPoint.GamePackageCard,
    path: PlatformRoutePath.GamePackageCard,
    redirect: {
      name: PlatformEnterPoint.GamePackage,
    },
    meta: {
      title: '游戏包体中心包体检测卡片',
    },
  },
  {
    name: PlatformEnterPoint.GamePackageDoctorProportion,
    path: PlatformRoutePath.GamePackageDoctorProportion,
    component: () => import('../../views/test/gamePackage/doctor/proportion/index.vue'),
    meta: {
      title: '游戏包体中心包体检测饼图',
    },
  },
  {
    name: PlatformEnterPoint.GamePackageDoctorTrend,
    path: PlatformRoutePath.GamePackageDoctorTrend,
    component: () => import('../../views/test/gamePackage/doctor/trend/index.vue'),
    meta: {
      title: '游戏包体中心包体检测趋势图',
    },
  },
  {
    name: PlatformEnterPoint.GamePackageVersionSimple,
    path: PlatformRoutePath.GamePackageVersionSimple,
    component: () => import('../../views/test/gamePackage/list/simpleCard/index.vue'),
    meta: {
      title: '游戏包体中心包版本简要信息',
    },
  },
  {
    name: PlatformEnterPoint.Tool,
    path: PlatformRoutePath.Tool,
    redirect: {
      name: PlatformEnterPoint.Toolkit,
    },
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.Toolkit],
      },
      title: '工具',
    },
  },
  {
    name: PlatformEnterPoint.Toolkit,
    path: PlatformRoutePath.Toolkit,
    component: () => import('../../views/toolkit/index.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.Toolkit],
      },
      title: '工具中心',
    },
  },
  {
    name: PlatformEnterPoint.ToolkitDetail,
    path: PlatformRoutePath.ToolkitDetail,
    component: () => import('../../views/toolkit/detail/index.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.ToolkitDetail],
      },
      title: '工具详情',
    },
  },
  {
    name: PlatformEnterPoint.ToolkitPackageSettings,
    path: PlatformRoutePath.ToolkitPackageSettings,
    component: () => import('../../views/toolkit/settings/index.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.ToolkitPackageSettings],
      },
      title: '工具商店配置',
    },
  },
  {
    name: PlatformEnterPoint.Tracking,
    path: PlatformRoutePath.Tracking,
    redirect: {
      name: PlatformEnterPoint.TrackingAnalysis,
    },
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.TrackingAnalysis],
      },
      title: '埋点',
    },
  },
  {
    name: PlatformEnterPoint.TrackingAnalysisSettings,
    path: PlatformRoutePath.TrackingAnalysisSettings,
    component: () => import('../../views/toolkit/tracking/settings/index.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.TrackingAnalysisSettings],
      },
      title: '配置',
    },
  },
  {
    name: PlatformEnterPoint.Oasis,
    path: PlatformRoutePath.Oasis,
    component: () => import('../../views/oasisManagement/Landing.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.Oasis],
      },
      title: 'Oasis',
    },
  },
  {
    name: PlatformEnterPoint.Instructions,
    path: PlatformRoutePath.Instructions,
    redirect: {
      name: PlatformEnterPoint.InstructionCombinations,
    },
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.InstructionCombinations],
      },
      title: '指令集',
    },
  },
  {
    name: PlatformEnterPoint.InstructionCombinations,
    path: PlatformRoutePath.InstructionCombinations,
    component: () => import('../../views/toolkit/pyScript/combination/index.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.InstructionCombinations],
      },
      title: '指令',
    },
  },
  {
    name: PlatformEnterPoint.InstructionComponents,
    path: PlatformRoutePath.InstructionComponents,
    component: () => import('../../views/toolkit/pyScript/component/index.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.InstructionComponents],
      },
      title: '元件',
    },
  },
  {
    name: PlatformEnterPoint.ToolNavigations,
    path: PlatformRoutePath.ToolNavigations,
    component: () => import('../../views/toolkit/navigation/index.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.ToolNavigations],
      },
      title: '常用网站',
    },
  },
  {
    name: PlatformEnterPoint.GroupChat,
    path: PlatformRoutePath.GroupChat,
    component: () => import('../../views/toolkit/groupChat/index.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.ToolNavigations],
      },
      title: '群聊配置',
      icon: 'icon-park-outline:copy-link',
      hideTab: false,
      hideBreadcrumb: false,
      hideChildrenInMenu: false,
    },
  },
  {
    name: PlatformEnterPoint.ToolGroupChat,
    path: PlatformRoutePath.ToolGroupChat,
    component: () => import('../../views/toolkit/ToolGroupChat/index.vue'),
    meta: {
      title: '工具反馈群配置',
      icon: 'icon-park-outline:copy-link',
      hideTab: false,
      hideBreadcrumb: false,
      hideChildrenInMenu: false,
    },
  },
  {
    name: PlatformEnterPoint.ToolWorkPlatform,
    path: PlatformRoutePath.ToolWorkPlatform,
    redirect: {
      name: PlatformEnterPoint.JenkinsAutoTask,
    },
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.JenkinsAutoTask],
      },
      title: '作业平台',
    },
  },
  {
    name: PlatformEnterPoint.JenkinsAutoTask,
    path: PlatformRoutePath.JenkinsAutoTask,
    component: () => import('../../views/toolkit/workPlatform/jenkinsAutoTask/index.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.JenkinsAutoTask],
      },
      title: '光照烘焙任务',
    },
  },
  {
    name: PlatformEnterPoint.OriginInstructions,
    path: PlatformRoutePath.OriginInstructions,
    redirect: {
      name: PlatformEnterPoint.Instructions,
    },
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.Instructions],
      },
      title: '指令集(旧)',
    },
  },
  {
    name: PlatformEnterPoint.DeptAsset,
    path: PlatformRoutePath.DeptAsset,
    redirect: {
      name: PlatformEnterPoint.DeptAssetApplyManagement,
    },
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.DeptAssetApplyManagement],
      },
      title: '部门资产',
    },
  },
  {
    name: PlatformEnterPoint.DeptAssetApplyManagement,
    path: PlatformRoutePath.DeptAssetApplyManagement,
    component: () => import('../../views/deptAsset/apply/index.vue'),
    props: () => ({
      isSettingsPage: false,
    }),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.DeptAssetApplyManagement],
      },
      title: '设备借用',
    },
  },
  {
    name: PlatformEnterPoint.MessageCenter,
    path: PlatformRoutePath.MessageCenter,
    redirect: {
      name: PlatformEnterPoint.MessageTemplate,
    },
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.MessageTemplate],
      },
      title: '消息中心',
    },
  },
  {
    name: PlatformEnterPoint.MessageTemplate,
    path: PlatformRoutePath.MessageTemplate,
    component: () => import('../../views/message/template/index.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.MessageTemplate],
      },
      title: '消息模板',
    },
  },
  {
    name: PlatformEnterPoint.P4Training,
    path: PlatformRoutePath.P4Training,
    component: () => import('../../views/p4Entry/index.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.P4Training],
      },
      title: 'P4培训',
    },
  },
  {
    name: PlatformEnterPoint.TCP4T,
    path: PlatformRoutePath.TCP4T,
    component: () => import('../../views/tcp4t/index.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.TCP4T],
      },
      title: '技术中心P4技能认证考试',
    },
  },
  {
    name: PlatformEnterPoint.P4Trains,
    path: PlatformRoutePath.P4Trains,
    component: () => import('../../views/p4Trains/index.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.P4Trains],
      },
      title: '技术中心P4V操作手册',
    },
  },
  {
    name: PlatformEnterPoint.P4Pass,
    path: PlatformRoutePath.P4Pass,
    component: () => import('../../views/p4Pass/index.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.P4Pass],
      },
      title: '技术中心P4通过成员统计页面',
    },
  },
  {
    name: PlatformEnterPoint.P4Onboarding,
    path: PlatformRoutePath.P4Onboarding,
    component: () => import('../../views/p4TouchProcess/index.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.P4Onboarding],
      },
      title: '技术中心P4接触流程',
    },
  },
  {
    name: PlatformEnterPoint.P4TouchProcess,
    path: PlatformRoutePath.P4TouchProcess,
    redirect: {
      name: PlatformEnterPoint.P4Onboarding,
    },
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.P4Onboarding],
      },
      title: '技术中心P4接触流程【旧】',
    },
  },
  {
    name: PlatformEnterPoint.System,
    path: PlatformRoutePath.System,
    redirect: {
      name: PlatformEnterPoint.AccountSettings,
    },
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.AccountSettings],
      },
      title: '系统管理',
    },
  },
  {
    name: PlatformEnterPoint.ResourceCheckSwitchesTemplate,
    path: PlatformRoutePath.ResourceCheckSwitchesTemplate,
    component: () => import('../../views/system/switchesTemplate/index.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.ResourceCheckSwitchesTemplate],
      },
      title: '功能开关模板',
    },
  },
  {
    name: PlatformEnterPoint.ResourceCheckTemplate,
    path: PlatformRoutePath.ResourceCheckTemplate,
    component: () => import('../../views/system/resourceCheck/template/index.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.ResourceCheckTemplate],
      },
      title: '资源检查模板',
    },
  },
  {
    name: PlatformEnterPoint.DeptAssetsManagement,
    path: PlatformRoutePath.DeptAssetsManagement,
    redirect: {
      name: PlatformEnterPoint.DeviceManagement,
    },
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.DeviceManagement],
      },
      title: '设备中心管理',
    },
  },
  {
    name: PlatformEnterPoint.DeviceManagement,
    path: PlatformRoutePath.DeviceManagement,
    component: () => import('../../views/deptAsset/apply/index.vue'),
    props: () => ({
      isSettingsPage: true,
    }),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.DeviceManagement],
      },
      title: '设备管理',
    },
  },
  {
    name: PlatformEnterPoint.DeviceManagementAdminConfig,
    path: PlatformRoutePath.DeviceManagementAdminConfig,
    component: () => import('../../views/deptAsset/apply/settings/AdminConfig.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.DeviceManagement],
      },
      title: '管理员配置',
    },
  },
  {
    name: PlatformEnterPoint.DeviceManagementLogs,
    path: PlatformRoutePath.DeviceManagementLogs,
    component: () => import('../../views/deptAsset/apply/settings/Logs.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.DeviceManagement],
      },
      title: '使用记录',
    },
  },
  {
    name: PlatformEnterPoint.DeviceManagementFaultList,
    path: PlatformRoutePath.DeviceManagementFaultList,
    component: () => import('../../views/deptAsset/apply/settings/FaultList.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.DeviceManagement],
      },
      title: '报障列表',
    },
  },
  {
    name: PlatformEnterPoint.OrganizationManagement,
    path: PlatformRoutePath.OrganizationManagement,
    redirect: {
      name: PlatformEnterPoint.DeptManagement,
    },
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.DeptManagement],
      },
      title: '组织架构',
    },
  },
  {
    name: PlatformEnterPoint.DeptManagement,
    path: PlatformRoutePath.DeptManagement,
    component: () => import('../../views/system/deptAssets/dept/index.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.DeptManagement],
      },
      title: '部门管理',
    },
  },
  {
    name: PlatformEnterPoint.DeptMemberManagement,
    path: PlatformRoutePath.DeptMemberManagement,
    component: () => import('../../views/system/deptAssets/member/index.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.DeptMemberManagement],
      },
      title: '干员管理',
    },
  },
  {
    name: PlatformEnterPoint.P4TriggersSettings,
    path: PlatformRoutePath.P4TriggersSettings,
    redirect: {
      name: PlatformEnterPoint.P4TriggersOverview,
    },
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.P4TriggersOverview],
      },
      title: 'P4 Triggers配置',
    },
  },
  {
    name: PlatformEnterPoint.P4TriggersOverview,
    path: PlatformRoutePath.P4TriggersOverview,
    component: () => import('../../views/submitConf/p4Trigger/index.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.P4TriggersOverview],
      },
      title: 'P4 Triggers总览',
    },
  },
  {
    name: PlatformEnterPoint.P4TriggersParamsSettings,
    path: PlatformRoutePath.P4TriggersParamsSettings,
    component: () => import('../../views/system/p4TriggerSettings/index.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.P4TriggersParamsSettings],
      },
      title: '参数配置',
    },
  },
  {
    name: PlatformEnterPoint.PerforceSettings,
    path: PlatformRoutePath.PerforceSettings,
    redirect: {
      name: PlatformEnterPoint.PerforceServersSettings,
    },
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.PerforceServersSettings],
      },
      title: 'P4权限配置',
    },
  },
  {
    name: PlatformEnterPoint.SystemPerforceManagement,
    path: PlatformRoutePath.SystemPerforceManagement,
    component: () => import('../../views/perforce/permissions/index.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.SystemPerforceManagement],
      },
      title: '系统权限管理',
    },
  },
  {
    name: PlatformEnterPoint.PerforceServersSettings,
    path: PlatformRoutePath.PerforceServersSettings,
    component: () => import('../../views/system/perforceSettings/servers/index.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.PerforceServersSettings],
      },
      title: '服务器配置',
    },
  },
  {
    name: PlatformEnterPoint.PerforceAccessLevelsSettings,
    path: PlatformRoutePath.PerforceAccessLevelsSettings,
    component: () => import('../../views/system/perforceSettings/accessLevels/index.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.PerforceAccessLevelsSettings],
      },
      title: '权限类别配置',
    },
  },
  {
    name: PlatformEnterPoint.SecureSettings,
    path: PlatformRoutePath.SecureSettings,
    redirect: {
      name: PlatformEnterPoint.SecureChannelsSettings,
    },
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.SecureChannelsSettings],
      },
      title: '游戏安全配置',
    },
  },
  {
    name: PlatformEnterPoint.SecureChannelsSettings,
    path: PlatformRoutePath.SecureChannelsSettings,
    component: () => import('../../views/system/secure/channels/index.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.SecureChannelsSettings],
      },
      title: '渠道配置',
    },
  },
  {
    name: PlatformEnterPoint.TCP4TSettings,
    path: PlatformRoutePath.TCP4TSettings,
    redirect: {
      name: PlatformEnterPoint.TCP4TSelectionsSettings,
    },
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.TCP4TSelectionsSettings],
      },
      title: 'P4培训和考试管理',
    },
  },
  {
    name: PlatformEnterPoint.TCP4TSelectionsSettings,
    path: PlatformRoutePath.TCP4TSelectionsSettings,
    component: () => import('../../views/system/tcp4t/selections/index.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.TCP4TSelectionsSettings],
      },
      title: '选择题',
    },
  },
  {
    name: PlatformEnterPoint.TCP4TOperationsSettings,
    path: PlatformRoutePath.TCP4TOperationsSettings,
    component: () => import('../../views/system/tcp4t/operations/index.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.TCP4TOperationsSettings],
      },
      title: '操作题',
    },
  },
  {
    name: PlatformEnterPoint.Oasis,
    path: PlatformRoutePath.Oasis,
    component: () => import('../../views/oasisManagement/Landing.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.Oasis],
      },
      title: 'Oasis',
    },
  },
  {
    name: PlatformEnterPoint.P4TrainsSettings,
    path: PlatformRoutePath.P4TrainsSettings,
    component: () => import('../../views/system/tcp4t/trains/index.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.P4TrainsSettings],
      },
      title: 'P4V操作手册管理',
    },
  },
  {
    name: PlatformEnterPoint.P4OnboardingSettings,
    path: PlatformRoutePath.P4OnboardingSettings,
    component: () => import('../../views/system/tcp4t/touchProcess/index.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.P4OnboardingSettings],
      },
      title: '接触流程文档管理',
    },
  },
  {
    name: PlatformEnterPoint.P4OnboardingSettingsChild,
    path: PlatformRoutePath.P4OnboardingSettingsChild,
    component: () => import('../../views/system/tcp4t/touchProcess/child/index.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.P4OnboardingSettingsChild],
      },
      title: '接触流程文档管理子项目配置',
    },
  },
  {
    name: PlatformEnterPoint.ProjectsManagement,
    path: PlatformRoutePath.ProjectsManagement,
    component: () => import('../../views/system/project/index.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.ProjectsManagement],
      },
      title: '项目管理',
    },
  },
  {
    name: PlatformEnterPoint.ProjectPermissionManagement,
    path: PlatformRoutePath.ProjectPermissionManagement,
    component: () => import('../../views/system/project/permission/index.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.ProjectPermissionManagement],
      },
      title: '项目干员权限管理',
    },
  },
  {
    name: PlatformEnterPoint.ProjectMember,
    path: PlatformRoutePath.ProjectMember,
    component: () => import('../../views/system/project/permission/index.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.ProjectMember],
      },
      title: '项目干员管理',
    },
  },
  {
    name: PlatformEnterPoint.HomeSettings,
    path: PlatformRoutePath.HomeSettings,
    component: () => import('../../views/system/homeSettings/index.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.HomeSettings],
      },
      title: '首页配置',
    },
  },
  {
    name: PlatformEnterPoint.HomeSettingsChild,
    path: PlatformRoutePath.HomeSettingsChild,
    component: () => import('../../views/system/homeSettings/child/index.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.HomeSettingsChild],
      },
      title: '子功能配置',
    },
  },
  {
    name: PlatformEnterPoint.RoleManage,
    path: PlatformRoutePath.RoleManage,
    component: () => import('../../views/system/role/index.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.RoleManage],
      },
      title: '角色管理',
    },
  },
  {
    name: PlatformEnterPoint.AccountSettings,
    path: PlatformRoutePath.AccountSettings,
    component: () => import('../../views/system/account/settings/index.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.AccountSettings],
      },
      title: '个人设置',
    },
  },
  {
    name: PlatformEnterPoint.MenuManagement,
    path: PlatformRoutePath.MenuManagement,
    component: () => import('../../views/system/menu/index.vue'),
    meta: {
      permissionDeclare: {
        all: [PermissionPoint.MenuManagement],
      },
      title: '菜单管理',
    },
  },
];
