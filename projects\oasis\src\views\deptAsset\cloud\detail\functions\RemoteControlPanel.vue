<template>
  <div class="grid grid-cols-2 h-full gap-4 overflow-y-auto px-4">
    <!-- 输入文本区域 -->
    <ACard v-if="cloudDeviceStore.isAndroid" title="输入文本">
      <AAlert message="清除输入框与实时输入仅在Sonic输入法开启时可用" class="c-FO-Content-Text2" type="info" showIcon :closable="false" />
      <div class="mt-3 flex flex-wrap justify-center gap-2 text-center">
        <AButton type="primary" @click="sendText">
          清除输入框
        </AButton>
        <AButton type="primary" @click="startKeyboard">
          启动Sonic输入法
        </AButton>
        <AButton type="primary" @click="stopKeyboard">
          禁用Sonic输入法
        </AButton>
      </div>
    </ACard>

    <!-- 远程ADB区域 -->
    <ACard v-if="cloudDeviceStore.isAndroid" title="远程ADB">
      <div class="my-4">
        <ASpin :spinning="websocketStore.remoteAdbLoading">
          <div v-if="websocketStore.remoteAdbUrl" class="my-5">
            <div
              class="cursor-pointer rounded-md bg-[#303133] p-4"
              @click="() => handleCopy(`adb connect ${websocketStore.remoteAdbUrl}`)"
            >
              <span class="c-FO-Content-Components1 font-bold">
                adb connect {{ websocketStore.remoteAdbUrl }}
              </span>
            </div>
          </div>
          <div v-else>
            <ACard>
              <strong>driver未初始化成功</strong>
            </ACard>
          </div>
        </ASpin>
      </div>
    </ACard>
    <ACard v-else>
      <template #title>
        <div class="tab-btn-wrapper">
          <div
            class="tab-btn"
            :class="{ 'tab-btn--active': activeRemoteTab === 'SIB' }"
            @click="() => activeRemoteTab = 'SIB'"
          >
            远程SIB
          </div>
          <div
            class="tab-btn"
            :class="{ 'tab-btn--active': activeRemoteTab === 'WDA' }"
            @click="() => activeRemoteTab = 'WDA'"
          >
            远程WDA
          </div>
        </div>
      </template>
      <div class="my-4">
        <ASpin :spinning="websocketStore.remoteAdbLoading">
          <div v-if="iOSRemoteControlUrl" class="my-5">
            <div
              class="cursor-pointer rounded-md bg-[#303133] p-4"
              @click="() => handleCopy(iOSRemoteControlUrl)"
            >
              <span class="c-FO-Content-Components1 font-bold">
                {{ iOSRemoteControlUrl }}
              </span>
            </div>
          </div>
          <div v-else>
            <ACard>
              <strong>driver未初始化成功</strong>
            </ACard>
          </div>
        </ASpin>
      </div>
    </ACard>

    <!-- 其他功能区域 -->
    <ACard v-if="cloudDeviceStore.isAndroid" title="其他">
      <div class="text-center">
        <div class="my-2 flex flex-wrap items-center justify-center gap-2">
          <AButton
            type="primary" :disabled="websocketStore.isDriverFinish" :loading="websocketStore.driverLoading"
            @click="openDriver"
          >
            初始化UIAutomator2Server
          </AButton>
          <AButton type="primary" danger :disabled="!websocketStore.isDriverFinish" @click="closeDriver">
            停止UIAutomator2Server
          </AButton>
        </div>
        <div class="mt-2">
          Status:
          <span :class="websocketStore.isDriverFinish ? 'c-FO-Functional-Success1-Default' : 'c-FO-Content-Text2'">
            {{ websocketStore.isDriverFinish ? 'Connected' : 'Disconnected' }}
          </span>
        </div>
      </div>
    </ACard>

    <!-- 剪贴板操作区域 -->
    <ACard title="剪贴板操作">
      <ATextarea
        v-model:value="websocketStore.pasteText" :rows="10" :maxLength="100000" showCount allowClear
        placeholder="请输入文本内容"
      />
      <div class="mt-4 text-center">
        <AButton type="primary" @click="setPasteboard(websocketStore.pasteText)">
          发送到剪贴板
        </AButton>
        <AButton type="primary" class="ml-2" @click="getPasteboard">
          获取剪贴板文本
        </AButton>
      </div>
    </ACard>
  </div>
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue';
import {
  Alert as AAlert,
  Button as AButton,
  Card as ACard,
  Spin as ASpin,
  Textarea as ATextarea,
  message,
} from 'ant-design-vue';
import { useCloudDeviceStore, useCloudDeviceWebsocketStore } from '../../stores';
import { useClipboard } from '@vueuse/core';
import { useRemoteControl } from '../../composables/useRemoteControl';

const cloudDeviceStore = useCloudDeviceStore();
const websocketStore = useCloudDeviceWebsocketStore();

const {
  sendText,
  startKeyboard,
  stopKeyboard,
  setPasteboard,
  getPasteboard,
  openDriver,
  closeDriver,
} = useRemoteControl();

const { copy } = useClipboard();
const activeRemoteTab = ref('SIB');

function handleCopy(text: string) {
  copy(text);
  message.success('复制成功');
}

const remoteSIBUrl = computed(() => {
  return `sib remote connect --host ${cloudDeviceStore.agent?.host} -p ${websocketStore.remoteSIBPort}`;
});

const remoteWDAUrl = computed(() => {
  return `http://${cloudDeviceStore.agent?.host}:${websocketStore.remoteWDAPort}`;
});

const iOSRemoteControlUrl = computed(() => {
  return activeRemoteTab.value === 'SIB' ? remoteSIBUrl.value : remoteWDAUrl.value;
});
</script>

<style lang="less" scoped>
::v-deep(.ant-card-head) {
  @apply !p-0 h-14 overflow-hidden;

  & .ant-card-head-wrapper {
    @apply h-full;

    & .ant-card-head-title {
      @apply h-full flex items-center justify-center;
    }
  }
}

.tab-btn-wrapper {
  @apply h-full w-full flex items-center;

  & .tab-btn {
    @apply h-full flex flex-1 cursor-pointer items-center justify-center b-b-1 b-b-FO-Container-Fill0  font-bold hover:c-FO-Brand-Primary-Default;
    background-color: var(--card-background-color);

    &--active {
      @apply c-FO-Brand-Primary-Default bg-FO-Container-Fill0 !b-b-FO-Brand-Primary-Default;
    }
  }
}
</style>
